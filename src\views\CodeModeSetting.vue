<!-- 命名模式配置 -->
<template>
  <CusFullModal
    :visible="modal可见"
    modal-animation-name="zoom-lb"
    @open="打开model()"
    @cancel="modal取消()"
    @close="modal关闭动画结束()"
  >
    <template #header_left>
      <span class="font-500">命名翻译设置</span>
    </template>

    <template #header_right>
      <ModalTitleCloseBtn @click="modal取消()" />
    </template>

    <div class="code_mode_setting_wrapper" p="x-20px y-16px">
      <a-alert class="mb-16px">
        主页
        <span class="text_important">右键</span>
        点击左下角的
        <i i-tabler-code class="text-20px" />、
        <i i-tabler-code-off class="text-20px" />
        , 或设置页的对应按钮, 均可进入本页面<br>
        你可以根据自己的习惯, [启用/停用]某个命名模式的关键字, 甚至是完全关闭某个模式
      </a-alert>
      <a-table
        :data="设置数据"
        :pagination="false"
        :draggable="{ type: 'handle', width: 40 }"
        @change="表格数据变化($event)"
      >
        <template #columns>
          <a-table-column title="命名方式" data-index="remark" />
          <a-table-column title="关键字" data-index="keyConfig.cmds" />
          <a-table-column :width="120" align="center" data-index="enable">
            <template #title>
              <div class="table_title">
                <span>功能状态</span>
                <QuestionTooltip
                  content="关闭后, 表示对应的功能从插件中消失, 关键字失效, 命名模式下拉选项中也不会存在"
                />
              </div>
            </template>
            <template #cell="{ record }">
              <a-switch
                v-model="record.enable"
                :disabled="就剩一个开启了 && record.enable === true"
                @change="切换启用状态($event as boolean, record)"
              />
            </template>
          </a-table-column>
          <a-table-column :width="120" align="center" data-index="keyEnable">
            <template #title>
              <div class="table_title">
                <span>普通关键字</span>
                <QuestionTooltip
                  content="关闭后, 表示对应的功能无法使用关键字进入, 但依旧可以在命名模式下的下拉选项中找到它, 并正常使用"
                />
              </div>
            </template>
            <template #cell="{ record }">
              <a-switch
                v-model="record.keyEnable"
                :disabled="record.enable === false"
              />
            </template>
          </a-table-column>
          <a-table-column :width="120" align="center" data-index="anyKeyEnable">
            <template #title>
              <div class="table_title">
                <span>空心关键字</span>
                <QuestionTooltip
                  content="开启后, uTools将对输入的任何文字进行匹配, 在uTools输入文字后会出现对应模式的选项, 选择即可直接带入易翻对应的命名翻译模式。因为会匹配任意文字, 所以不建议开多了"
                  position="tr"
                />
              </div>
            </template>
            <template #cell="{ record }">
              <a-switch
                v-model="record.anyKeyEnable"
                :disabled="record.enable === false"
              />
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>

    <template #footer_left>
      <a-popconfirm
        position="tl"
        content-class="popconfirm_wrapper"
        type="warning"
        content="恢复命名翻译的默认配置"
        @ok="恢复默认配置()"
      >
        <a-button type="outline" status="danger">
          恢复关键字默认配置
        </a-button>
      </a-popconfirm>
    </template>
    <template #footer_right>
      <a-button @click="modal取消()">
        取消
      </a-button>
      <a-button type="primary" @click="设置modal确定()">
        确定
      </a-button>
    </template>
  </CusFullModal>
</template>

<script lang="ts" setup>
import { 命名模式配置存储 } from '@/store/useNameSetting'
import { Message } from '@arco-design/web-vue'
import { cloneDeep } from 'lodash-es'

defineOptions({
  name: 'CodeModeSetting',
})
const 命名模式配置 = 命名模式配置存储()
const modal可见 = ref(false)
const 设置数据 = ref<any[]>([])
const 就剩一个开启了 = computed(() => {
  return 设置数据.value.filter(({ enable }) => enable).length === 1
})

function 切换启用状态(val: boolean, record: any) {
  if (!val) {
    record.keyEnable = false
    record.anyKeyEnable = false
  }
}

function 表格数据变化(tableData: any) {
  设置数据.value = cloneDeep(tableData)
}

function 设置modal确定() {
  命名模式配置.保存数据(设置数据.value)
  Message.success('保存成功')
  modal取消()
}

// 点击弹框取消
function modal取消() {
  modal可见.value = false
}

function 打开model() {
  modal可见.value = true
}

function modal关闭动画结束() {}

function getSetData() {
  设置数据.value = cloneDeep(命名模式配置.命名翻译设置数据)
  // console.log('设置数据.value', 设置数据.value)
}

function 恢复默认配置() {
  命名模式配置.reset()
  getSetData()
  Message.success('重置成功')
}

defineExpose({
  打开model,
})

onMounted(() => {
  getSetData()
})
</script>

<style scoped lang="scss">
.table_title {
  @apply flex-y-c space-x-4px;
}

// .code_mode_setting_wrapper:deep(.arco-table-td) {
//   background: transparent;
// }
</style>
