<template>
  <svg
    class="loading_main"
    viewBox="0 0 128 128"
    width="128px"
    height="128px"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs>
      <linearGradient id="grad1" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stop-color="#000" />
        <stop offset="40%" stop-color="#fff" />
        <stop offset="100%" stop-color="#fff" />
      </linearGradient>
      <linearGradient id="grad2" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stop-color="#000" />
        <stop offset="60%" stop-color="#000" />
        <stop offset="100%" stop-color="#fff" />
      </linearGradient>
      <mask id="mask1">
        <rect x="0" y="0" width="128" height="128" fill="url(#grad1)" />
      </mask>
      <mask id="mask2">
        <rect x="0" y="0" width="128" height="128" fill="url(#grad2)" />
      </mask>
    </defs>
    <g fill="none" stroke-linecap="round" stroke-width="16">
      <circle
        class="loading_ring"
        stroke="#17181c26 dark:#e7edfe1a"
        r="56"
        cx="64"
        cy="64"
      />
      <g stroke="primary">
        <path
          class="loading_worm1"
          d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
          stroke="primary"
          stroke-dasharray="43.98 307.87"
        />
        <g transform="translate(42,42)">
          <g class="loading_worm2" transform="translate(-42,0)">
            <path
              class="loading_worm2-1"
              d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
              stroke-dasharray="43.98 175.92"
            />
          </g>
        </g>
      </g>
      <g stroke="#25c7f4" mask="url(#mask1)">
        <path
          class="loading_worm1"
          d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
          stroke-dasharray="43.98 307.87"
        />
        <g transform="translate(42,42)">
          <g class="loading_worm2" transform="translate(-42,0)">
            <path
              class="loading_worm2-1"
              d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
              stroke-dasharray="43.98 175.92"
            />
          </g>
        </g>
      </g>
      <g stroke="primary" mask="url(#mask2)">
        <path
          class="loading_worm1"
          d="M120,64c0,30.928-25.072,56-56,56S8,94.928,8,64"
          stroke-dasharray="43.98 307.87"
        />
        <g transform="translate(42,42)">
          <g class="loading_worm2" transform="translate(-42,0)">
            <path
              class="loading_worm2-1"
              d="M8,22c0-7.732,6.268-14,14-14s14,6.268,14,14"
              stroke-dasharray="43.98 175.92"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
main {
  padding: 1.5em 0;
}

.loading_main {
  display: block;
  width: 60px;
  height: 60px;
}

.loading_ring {
  transition: stroke 0.3s;
}

.loading_worm1,
.loading_worm2,
.loading_worm2-1 {
  animation: worm1 4.5s ease-in infinite;
}

.loading_worm1 {
  transform-origin: 64px 64px;
}

.loading_worm2,
.loading_worm2-1 {
  transform-origin: 22px 22px;
}

.loading_worm2 {
  animation-name: worm2;
  animation-timing-function: linear;
}

.loading_worm2-1 {
  animation-name: worm2-1;
  stroke-dashoffset: 175.92;
}

/* Animations */
@keyframes worm1 {
  0%,
  100% {
    stroke-dashoffset: 0;
  }

  12.5% {
    animation-timing-function: ease-out;
    stroke-dashoffset: -175.91;
  }

  25% {
    animation-timing-function: cubic-bezier(0, 0, 0.43, 1);
    stroke-dashoffset: -307.88;
  }

  50% {
    animation-timing-function: ease-in;
    stroke-dashoffset: -483.8;
  }

  62.5% {
    animation-timing-function: ease-out;
    stroke-dashoffset: -307.88;
  }

  75% {
    animation-timing-function: cubic-bezier(0, 0, 0.43, 1);
    stroke-dashoffset: -175.91;
  }
}

@keyframes worm2 {
  0%,
  12.5%,
  75%,
  100% {
    transform: rotate(0) translate(-42px, 0);
  }

  25%,
  62.5% {
    transform: rotate(0.5turn) translate(-42px, 0);
  }
}

@keyframes worm2-1 {
  0% {
    stroke-dashoffset: 175.91;
    transform: rotate(0);
  }

  12.5% {
    animation-timing-function: cubic-bezier(0, 0, 0.42, 1);
    stroke-dashoffset: 0;
    transform: rotate(0);
  }

  25% {
    animation-timing-function: linear;
    stroke-dashoffset: 0;
    transform: rotate(1.5turn);
  }

  37.5%,
  50% {
    stroke-dashoffset: -175.91;
    transform: rotate(1.5turn);
  }

  62.5% {
    animation-timing-function: cubic-bezier(0, 0, 0.42, 1);
    stroke-dashoffset: 0;
    transform: rotate(1.5turn);
  }

  75% {
    animation-timing-function: linear;
    stroke-dashoffset: 0;
    transform: rotate(0);
  }

  87.5%,
  100% {
    stroke-dashoffset: 175.92;
    transform: rotate(0);
  }
}
</style>
