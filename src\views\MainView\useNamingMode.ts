/** 命名翻译模式相关业务 */
import { 用户配置存储 } from '@MainView/MainViewData'
import { 切换类型数组 } from '@/assets/changeCaseMap'
import { useGlobalStore } from '@/store/globalData'
import { 命名模式配置存储 } from '@/store/useNameSetting'
import type { AnyFunction } from '@/types'

export function use命名模式模块(打开命名模式设置: AnyFunction) {
  const { baseConfig, saveBaseConfigByKey } = 用户配置存储()
  const 命名模式配置 = 命名模式配置存储()
  const 是命名模式 = computed(() => baseConfig.codeMode) // 命名翻译模式

  const 已启用的切换类型数组 = computed(() => {
    return 命名模式配置.命名翻译设置数据.filter((item: any) => item.enable)
  })
  const asd = ref('')
  const 命名模式类型 = computed({
    get() {
      if (asd.value && 已启用的切换类型数组.value.find(item => item.name === asd.value)) {
        return asd.value
      }
      else {
        return 已启用的切换类型数组.value[0]?.name
      }
    },
    set(val) {
      asd.value = val
    },
  })
  function 获取命名模式对应结果() {
    const { 翻译结果 } = storeToRefs(useGlobalStore())
    return 返回命名模式对应结果(翻译结果.value, 命名模式类型.value)
  }

  // 获取命名翻译模式的翻译结果
  function 返回命名模式对应结果(文字 = '', type = 'camelCase') {
    const 当前模式对象 = 切换类型数组.find(item => item.name === type)
    if (!文字 || !当前模式对象) {
      return 文字
    }
    const 换行符 = 文字.includes('\r\n') ? '\r\n' : 文字.includes('\r') ? '\r' : '\n'

    const 文字数组 = 文字.split(换行符)
    const 结果数组 = 文字数组.map(item => 当前模式对象.handle(item))
    return 结果数组.join(换行符)
  }

  // 根据关键字切换命名翻译模式
  function 改变命名模式类型(code: string) {
    // codeMode&xx
    const reg = /^codeMode__/
    if (code === 'codeMode__setting') {
      // 命名模式设置
      打开命名模式设置()
    }
    else if (reg.test(code)) {
      saveBaseConfigByKey('codeMode', true)
      const modeName = code.split('__')[1]
      console.log('🚀 ~ modeName >>>', modeName)
      命名模式类型.value = modeName
    }
    else {
      saveBaseConfigByKey('codeMode', false)
    }
  }

  return {
    是命名模式,
    命名模式类型,
    切换类型数组,
    已启用的切换类型数组,
    获取命名模式对应结果,
    改变命名模式类型,
    返回命名模式对应结果,
  }
}
