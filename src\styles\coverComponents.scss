// 覆盖组件库的样式文件

// Modal性能优化
.arco-modal {
  will-change: transform, opacity;
}

.arco-modal-mask {
  will-change: backdrop-filter;
}

// Tooltip组件的最大宽度, 防止内容过长
.arco-tooltip-content {
  max-width: 250px;
}

// 二次确认弹窗, 所有的写死300px, 没有动态宽度
.arco-popover-popup-content, .popconfirm_wrapper {
  width: 300px;
}

// 不让Modal外部出现滚动条, 优化动画效果, 因为该项目不会出现Modal中内容很多需要滚动Modal的情况
.arco-modal-wrapper {
  overflow: hidden;
}

// Message组件的样式
.arco-message {
  border-radius: 8px;
  box-shadow: 5px 5px 12px var(--mimicry-btn-shadow-color-a), -5px -5px 12px var(--mimicry-btn-shadow-color-b);
}

// 没有头部的Modal样式
.no_header_modal {
  .arco-modal-header {
    display: none;
  }
}

// 全屏的Modal都不要圆角
.arco-modal-fullscreen {
  border-radius: 0;
}

.image_footer, .tools_wrapper {
  .arco-select-view-value {
    display: grid;
    text-align: center;
    font-family: iconfont, var(--main-font-family) !important;
  }
}

// Modal的阴影
.modal_shadow {
  box-shadow: 0 10px 36px var(--setting-small-modal-shadow-color);
}

// 引导层样式
.guide_wrapper {
  @apply bg-#fff I_shadow-[0_4px_29px_#0000003d] dark:(bg-#2a2a2b I_shadow-[0_4px_29px_#090909]);

  .shepherd-header {
    @apply I_bg-transparent;
  }

  .shepherd-text, .shepherd-header, .shepherd-footer {
    font-size: 14px;
    padding-top: 10px;
  }

  .shepherd-content {
    @apply bg-#fff dark:bg-#2a2a2b;
  }

  .shepherd-text {
    @apply text-[#333] leading-relaxed pt-0 dark:text-#fff;
  }

  .shepherd-title {
    @apply font-600 text-18px dark:(I_text-#fff font-normal);
  }

  .shepherd-button {
    @apply bg-primary text-#fff py-4px px-16px rounded-2px;

    &:hover {
      filter: contrast(150%);
    }
  }

  .shepherd-button:not(:disabled):hover {
    @apply bg-primary:70;
  }

  .shepherd-arrow {
    display: none;
  }
}

// 引导层的遮罩层样式
.shepherd-modal-overlay-container {
  opacity: 0.4 !important;
}
