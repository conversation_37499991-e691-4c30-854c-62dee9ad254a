import { detect } from 'tinyld'
import { 汉字和汉字符号正则 } from '@/views/MainView/MainViewData'
import { 获取推断语言名称 } from '@/utils/language'

export async function localDetectLang(text: string) {
  if (text.length > 200) {
    text = text.slice(0, 200)
  }
  const langWeights = {
    en: 0,
    zh: 0,
    ko: 0,
    th: 0,
    ja: 0,
    ru: 0,
    es: 0,
    fr: 0,
    de: 0,
  }

  for (const char of text.split('')) {
    if (汉字和汉字符号正则.test(char)) {
      // Detect Chinese
      langWeights.zh += 1
      langWeights.ja += 1
    }
    if (/[\uAC00-\uD7A3]/.test(char)) {
      // Detect Korean
      langWeights.ko += 1
    }
    if (/[\u0E01-\u0E5B]/.test(char)) {
      // Detect Thai
      langWeights.th += 1
    }
    if (/[\u3040-\u30FF]/.test(char)) {
      // Detect Japanese
      langWeights.ja += 1
    }
    if (/[\u0400-\u04FF]/.test(char)) {
      // Detect Russian
      langWeights.ru += 1
    }
    if (/[áéíóúüñ]/.test(char)) {
      // Detect Spanish
      langWeights.es += 1
    }
    if (/[àâçéèêëîïôûùüÿœæ]/.test(char)) {
      // Detect French
      langWeights.fr += 1
    }
    if (/[äöüß]/.test(char)) {
      // Detect German
      langWeights.de += 1
    }
  }
  for (const char of text.split(' ')) {
    if (/[a-z]/i.test(char)) {
      // Detect English
      langWeights.en += 1
      langWeights.es += 1
      langWeights.fr += 1
      langWeights.de += 1
    }
  }
  if (langWeights.zh > 0 && langWeights.zh === langWeights.ja) {
    // fix pure Chinese text
    langWeights.zh += 1
  }
  if (
    langWeights.en > 0
    && langWeights.en === langWeights.es
    && langWeights.en === langWeights.fr
    && langWeights.en === langWeights.de
  ) {
    // fix pure English text
    langWeights.en += 1
  }
  const langWeightResult = Object.entries(langWeights).sort((a, b) => b[1] - a[1])[0]
  if (langWeightResult[1] === 0) {
    return 'en'
  }

  else {
    return langWeightResult[0]
  }
}

export async function 本地判断语种(text: string) {
  const 截取长度 = 200
  if (text.length > 截取长度) {
    text = text.slice(0, 截取长度)
  }
  const detectRes = detect(text)
  const detect推测结果 = 获取推断语言名称('google_2', detectRes)
  if (detect推测结果.languageText) {
    return detectRes
  }
  else {
    return await localDetectLang(text)
  }
}
