/**
 * 阿里翻译接口
 * https://help.aliyun.com/document_detail/97592.html
 */

import dayjs from 'dayjs'
import { type 翻译参数Type, 返回状态码及信息 } from '../common'
import { textTranslation as 谷歌翻译 } from './google'
import { 根据时间差计算网络时间 } from '@/apis/time'

/**
 * 机器翻译
 * @param {string} options.q 请求翻译query(UTF-8编码)
 * @param {string} options.from 翻译源语言(可设置为auto)
 * @param {string} options.to 翻译目标语言(不可设置为auto)
 * @param {object} options.keyConfig key配置
 */
export async function textTranslation({ q, from, to, keyConfig }: 翻译参数Type) {
  const dateValue = 根据时间差计算网络时间()

  const params = {
    Action: 'TranslateGeneral',
    SourceText: encodeURIComponent(q!),
    SourceLanguage: from,
    TargetLanguage: to,
    FormatType: 'text',
    Scene: 'general',
    date: dayjs(dateValue).toDate(),
  }

  if (window.servers) {
    return window.servers
      .aliTextTranslate(params, keyConfig)
      .then(async (res: any) => {
        const { Code, Data, Message } = res
        if (Code === '200') {
          return 返回状态码及信息(200, { text: Data.Translated })
        }
        else {
          // return 返回状态码及信息(500, null, Message)
          const { code: gCode, text: gText } = await 谷歌翻译({
            q: Message,
            from: 'auto',
            to: 'zh-CN',
          })
          return 返回状态码及信息(500, null, gCode === 200 && gText ? gText : Message)
        }
      })
      .catch(async (err: any) => {
        console.error(err)
        return 返回状态码及信息(500)
      })
  }
  else {
    return 返回状态码及信息(403)
  }
}
