<template>
  <Transition name="component-scale" mode="out-in">
    <template v-if="在线朗读显示条件">
      <div v-show="要显示按钮" class="flex space-x-8px">
        <!-- 播放按钮 -->
        <Transition name="component-scale" mode="out-in">
          <MimicryBtn :loading="在线朗读loading" @click="请求在线语音()">
            <i i-akar-icons-sound-on />
          </MimicryBtn>
        </Transition>

        <!-- 开始暂停按钮 -->
        <Transition name="component-scale" mode="out-in">
          <MimicryBtn v-show="在线音频Url" @click="开始暂停()">
            <i :class="[在线正在播放 ? 'i-pixelarticons-pause' : 'i-ri-play-fill']" />
          </MimicryBtn>
        </Transition>
      </div>
    </template>

    <template v-else-if="离线朗读显示条件">
      <div v-show="要显示按钮" class="flex space-x-8px">
        <!-- 播放按钮 -->
        <!-- 排查为什么不能停止播放了 -->
        <MimicryBtn @click="开启离线朗读()">
          <i
            :class="[
              离线朗读状态 === 'play' ? 'i-mingcute-stop-fill' : 'i-akar-icons-sound-on',
            ]"
          />
        </MimicryBtn>
      </div>
    </template>
  </Transition>
</template>

<script setup lang="ts">
import type { 语种 } from '@/assets/translateApiOption'
import type { 朗读类型 } from '@/types'
import { 用户配置存储 } from '@MainView/MainViewData'
import { 离线朗读主函数 } from '@MainView/useOutlineVoice'
import { 在线朗读主函数 } from '@MainView/useVoice'

const { code, text, languageType, btnType } = defineProps<{
  code: number
  text: string
  languageType: 语种
  btnType: 朗读类型
}>()

const emits = defineEmits<{
  startReadingOnline: [buttonType: 朗读类型]
  startReadOffline: [buttonType: 朗读类型]
}>()

const {
  在线朗读loading,
  在线播放函数,
  在线正在播放,
  在线音频Url,
  重置在线音频Url,
  在线朗读停止,
} = 在线朗读主函数()
const { 离线播放函数, 离线朗读状态, 支持离线朗读, 离线朗读停止 } = 离线朗读主函数()

const 用户配置 = 用户配置存储()
const 要显示按钮 = computed(() => {
  return !!text?.trim() && code === 200
})
const baseConfig = computed(() => 用户配置.baseConfig)
const 是命名模式 = computed(() => 用户配置.baseConfig.codeMode)
const 在线朗读显示条件 = computed(() => {
  return (
    baseConfig.value.readAloud
    && baseConfig.value.readingModel === '在线'
    && !是命名模式.value
  )
})

const 离线朗读显示条件 = computed(() => {
  return (
    baseConfig.value.readAloud
    && 支持离线朗读.value
    && baseConfig.value.readingModel === '离线'
    && !是命名模式.value
    && languageType === 'en'
  )
})

const 显示的按钮数量 = computed(() => {
  const 在线播放按钮Vis = 在线朗读显示条件.value && 要显示按钮.value
  const 离线播放按钮Vis = 离线朗读显示条件.value && 要显示按钮.value
  const 在线控制按钮Vis = !!在线音频Url.value
  if (在线播放按钮Vis && 在线控制按钮Vis) {
    return 2
  }
  return 在线播放按钮Vis || 离线播放按钮Vis ? 1 : 0
})

function 请求在线语音() {
  emits('startReadingOnline', btnType)
  在线播放函数(text, languageType)
}

function 开始暂停() {
  在线正在播放.value = !在线正在播放.value
  emits('startReadingOnline', btnType)
}

function 开启离线朗读() {
  emits('startReadOffline', btnType)
  离线播放函数(text)
}

defineExpose({
  在线朗读停止,
  离线朗读停止,
  重置在线音频Url,
  离线朗读状态,
  显示的按钮数量,
})
</script>

<style lang="scss" scoped></style>
