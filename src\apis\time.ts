import dayjs from 'dayjs'

/** 获取网络时间 */
export async function getWebTime() {
  const url = import.meta.env.VITE_WEBTIME
  const data = await fetch(url).then(res => res.json())
  const time = Number(data?.data?.t)
  return time ?? 0
}

export async function 获取并保存本地与网络时间差() {
  const webTime = await getWebTime()
  const diff = dayjs().diff(dayjs(webTime))
  window.sessionStorage.setItem('timeDiff', diff.toString())
}

/**
 *
 * @returns 时间戳
 */
export function 根据时间差计算网络时间() {
  const timeDiff = window.sessionStorage.getItem('timeDiff')
  const diffNum = Number(timeDiff)
  const dateValue = dayjs().diff(dayjs(diffNum))
  return dateValue
}
