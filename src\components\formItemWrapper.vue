<template>
  <div>
    <a-divider v-if="isServer" orientation="left">
      <div class="flex-c" :class="divideClass">
        <ApiIcon :data="iconData" class="translate-y-1px" />
        <span>{{ title }}</span>
      </div>
    </a-divider>
    <section class="w-full">
      <slot />
    </section>
  </div>
</template>

<script setup lang="ts">
import type { 翻译服务类型 } from '@/types'

interface formItemWrapperType {
  title?: string
  iconData?: 翻译服务类型
  divideClass?: string
  isServer?: boolean
}

// withDefaults(defineProps<formItemWrapperType>(), {
//   title: '标题',
//   iconData: 'baidu',
//   divideClass: 'space-x-4px',
//   isServer: false,
// })

const {
  title = '标题',
  iconData = 'baidu',
  divideClass = 'space-x-4px',
  isServer = false,
} = defineProps<formItemWrapperType>()
</script>

<style lang="scss" scoped></style>
