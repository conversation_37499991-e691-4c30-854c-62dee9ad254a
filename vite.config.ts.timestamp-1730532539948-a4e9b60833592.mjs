// vite.config.ts
import { resolve } from "node:path";
import process from "node:process";
import vue from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/@vitejs+plugin-vue@5.1.4_vite@5.4.9_vue@3.5.12/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import UnoCSS from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/unocss@0.63.6_postcss@8.4.47_typescript@5.6.3_vite@5.4.9/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/unplugin-auto-import@0.18.3_@vueuse+core@11.1.0/node_modules/unplugin-auto-import/dist/vite.js";
import { ArcoResolver } from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/unplugin-vue-components@0.27.4_vue@3.5.12/node_modules/unplugin-vue-components/dist/resolvers.js";
import Components from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/unplugin-vue-components@0.27.4_vue@3.5.12/node_modules/unplugin-vue-components/dist/vite.js";
import { defineConfig } from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/vite@5.4.9_@types+node@20.14.10_less@4.2.0_sass@1.80.3_terser@5.36.0/node_modules/vite/dist/node/index.js";
import obfuscatorPlugin from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/vite-plugin-javascript-obfuscator@3.1.0/node_modules/vite-plugin-javascript-obfuscator/dist/index.cjs.js";
import VueDevTools from "file:///Users/<USER>/learn/utools/%E6%98%93%E7%BF%BB/easy-translation/node_modules/.pnpm/vite-plugin-vue-devtools@7.5.3_vite@5.4.9_vue@3.5.12/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";

// src/config/colorConfig.ts
var primaryColor = "#5b61ff";

// vite.config.ts
function pathResolve(dir) {
  return resolve(process.cwd(), ".", dir);
}
var vite_config_default = defineConfig({
  base: "./",
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: { "arcoblue-6": primaryColor },
        javascriptEnabled: true
      },
      scss: {
        api: "modern-compiler"
        // or "modern", "legacy"
      }
    }
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ArcoResolver()],
      imports: ["vue", "@vueuse/core", "pinia"],
      dts: "src/auto-imports.d.ts",
      dirs: ["src/utils/common/"],
      vueTemplate: true
    }),
    UnoCSS(),
    Components({
      resolvers: [ArcoResolver()],
      dts: "src/components.d.ts"
    }),
    VueDevTools(),
    obfuscatorPlugin({
      include: [/\.(jsx?|tsx?|cjs|mjs|vue)$/],
      apply: "build",
      options: {
        // 配置详见: https://github.com/javascript-obfuscator/javascript-obfuscator#compact
        controlFlowFlattening: true,
        // 启用代码控制流扁平化。控制流扁平化是源代码的结构转换, 它阻碍了程序的理解。
        controlFlowFlatteningThreshold: 0.4,
        // 给定任意节点应用 controlFlowFlattening 转换的概率
        deadCodeInjection: true,
        // 将会在混淆代码中添加随机的死代码块
        deadCodeInjectionThreshold: 0.4,
        // 设置受死代码注入影响的节点百分比
        identifierNamesGenerator: "hexadecimal",
        // 生成类似 _0xabc123 的标识符名称
        simplify: true,
        // 通过简化代码实现代码混淆
        splitStrings: true,
        // 将字面字符串分割成长度为指定长度的拼接形式
        splitStringsChunkLength: 10,
        // 设置 splitStrings 选项的块长度。
        stringArrayCallsTransform: true,
        // 启用字符串数组调用转换。根据 stringArrayCallsTransformThreshold 值, 这些调用的所有参数都可能被提取为不同的对象
        stringArrayCallsTransformThreshold: 0.5,
        // 调整 stringArrayCallsTransform 的概率
        stringArrayEncoding: ["base64"],
        // 使用 base64 对 stringArray 的所有字符串字面值进行编码
        stringArrayWrappersCount: 2,
        // 设置 stringArray 在每个根作用域或函数作用域中的封装数
        stringArrayWrappersParametersMaxCount: 4,
        // 允许控制 stringArray 包装器参数的最大数量
        stringArrayWrappersType: "function",
        // 在每个作用域内的随机位置添加函数包装器
        stringArrayThreshold: 0.6,
        // 调整字符串文字插入到 stringArray 中的概率
        transformObjectKeys: true
        // 启用对象键的转换
      }
    })
  ],
  resolve: {
    alias: {
      "@": pathResolve("src"),
      "@imgs": pathResolve("src/assets/imgs"),
      "@MainView": pathResolve("src/views/MainView")
    }
  },
  build: {
    // 使用esbuild压缩
    // target: 'esnext',
    // minify: 'esbuild',
    // 使用terser压缩
    target: "es2021",
    minify: "terser",
    terserOptions: {
      ecma: 2020,
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          dep: [
            "@vueuse/core",
            "change-case",
            "dayjs",
            "lodash-es",
            "nanoid",
            "pinia",
            "qs",
            "vue"
          ],
          arco: ["@arco-design/web-vue"]
        }
      }
    }
  },
  server: {
    host: "0.0.0.0",
    port: 6789,
    open: true,
    proxy: {
      // 百度翻译
      "/baiduApi": {
        target: "https://fanyi-api.baidu.com/api/trans/vip/translate",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/baiduApi/, "")
      },
      // 腾讯翻译
      "/tencentApi": {
        target: "https://tmt.tencentcloudapi.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/tencentApi/, "")
      },
      // 有道翻译
      "/youdaoApi": {
        target: "http://openapi.youdao.com/api",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/youdaoApi/, "")
      },
      // 彩云小译
      "/caiyunApi": {
        target: "http://api.interpreter.caiyunai.com/v1/translator",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/caiyunApi/, "")
      },
      // 阿里翻译
      "/aliApi": {
        target: "http://mt.cn-hangzhou.aliyuncs.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/aliApi/, "")
      },
      // 火山翻译
      "/huoshanApi": {
        target: "https://open.volcengineapi.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/huoshanApi/, "")
      },
      // 接口
      "/uniapi": {
        target: "https://be392405-5b88-4143-ad6b-f155b106ab85.bspapp.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/uniapi/, "")
      },
      // 小牛翻译
      "/xiaoniu": {
        target: "https://api.niutrans.com/NiuTransServer/translation",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/xiaoniu/, "")
      },
      // 腾讯交互
      "/tencentTransmart": {
        target: "https://transmart.qq.com/api/imt",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/tencentTransmart/, "")
      },
      // deepl free
      "/deepl_free": {
        target: "https://api-free.deepl.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/deepl_free/, "")
      },
      // deepl free
      "/deepl_pro": {
        target: "https://api.deepl.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/deepl_pro/, "")
      },
      "/web_time": {
        target: "http://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/web_time/, "")
      },
      // 百度图片翻译
      "/baiduImg": {
        target: "https://fanyi-api.baidu.com/api/trans/sdk/picture",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/baiduImg/, "")
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
