import { getDbStorageItem } from '@/utils/storage'
import { cloneDeep } from 'lodash-es'
import { nanoid } from 'nanoid'

// 依赖sortOrder进行排序, 相同sortOrder的按照label进行排序。
export const apiOptions = [
  { label: '谷歌翻译', value: 'google', sortOrder: 0 },
  { label: '阿里翻译', value: 'ali', sortOrder: 10 },
  { label: '百度翻译', value: 'baidu', sortOrder: 10 },
  { label: '火山翻译', value: 'huoshan', sortOrder: 10 },
  { label: '腾讯翻译', value: 'tencent', sortOrder: 10 },
  { label: '腾讯交互', value: 'transmart', sortOrder: 10 },
  { label: '小牛翻译', value: 'xiaoniu', sortOrder: 10 },
  { label: '有道翻译', value: 'youdao', sortOrder: 10 },
  { label: 'DeepL', value: 'deepl', sortOrder: 10 },
  { label: '彩云小译', value: 'caiyun', sortOrder: 20 },
].sort((a, b) => a.sortOrder - b.sortOrder || a.label.localeCompare(b.label))

type ExcludeLanguages<T extends 语种, U extends 语种> = Exclude<T, U>
type YueWyw = ExcludeLanguages<语种, ExcludeLanguages<语种, 'yue' | 'wyw'>>[]
type 语种Array = typeof 所有语种 // 从数组中提取类型
export type 语种 = 语种Array[number] // 使用索引签名获取联合类型

// 定义所有语种的只读数组
const 所有语种 = ['zh', 'en', 'jp', 'ru', 'kor', 'de', 'fra', 'spa', 'it', 'pt', 'th', 'cht', 'yue', 'wyw'] as const
const 粤语和文言文: YueWyw = ['yue', 'wyw']// 因为很多服务不支持粤语和文言文, 所以这里单独列出来

interface KeyValueStringType {
  [key: string]: string
}

// 语种与中文对应
const 语种枚举: KeyValueStringType = {
  auto: '自动检测',
  zh: '中文-简',
  en: '英语',
  jp: '日语',
  ru: '俄语',
  kor: '韩语',
  de: '德语',
  fra: '法语',
  spa: '西班牙语',
  it: '意大利语',
  pt: '葡萄牙语',
  th: '泰语',
  cht: '中文-繁',
  yue: '粤语',
  wyw: '文言文',
}

// 语种简写映射
const 语种简写映射: KeyValueStringType = {
  '中文-简': '简中',
  '西班牙语': '西语',
  '意大利语': '意语',
  '葡萄牙语': '葡语',
  '中文-繁': '繁中',
  '文言文': '文言',
}

function 从所有语种中排除(arr: 语种[]): 语种[] {
  return 所有语种.filter(i => !arr.includes(i))
}

function 从所有语种中保留(arr: 语种[]): 语种[] {
  return 所有语种.filter(i => arr.includes(i))
}

function 返回排除同语种和粤语文言文的数组(语种tag: 语种) {
  return 从所有语种中保留([语种tag]).concat(粤语和文言文)
}

interface 不支持对象 {
  auto: 语种[]
  zh: 语种[]
  en: 语种[]
  jp: 语种[]
  ru: 语种[]
  kor?: 语种[]
  de?: 语种[]
  fra?: 语种[]
  spa?: 语种[]
  it?: 语种[]
  pt?: 语种[]
  th?: 语种[]
  yue?: 语种[]
  cht?: 语种[]
}

interface 大对象item基础类型 {
  from不支持: string[]
  to不支持: string[] | undefined
  自定义不支持: 不支持对象 | undefined
}

interface 大对象item类型 {
  [key: string]: 大对象item基础类型
}

// 下面因为定义的都是「不」支持的, 所以函数名语义上是反的, 实际上是正确的
export const api不支持的大对象: 大对象item类型 = {
  google: {
    from不支持: 粤语和文言文,
    to不支持: 粤语和文言文,
    自定义不支持: undefined,
  },
  transmart: {
    from不支持: 粤语和文言文,
    自定义不支持: {
      auto: 粤语和文言文,
      zh: 返回排除同语种和粤语文言文的数组('zh'),
      en: 返回排除同语种和粤语文言文的数组('en'),
      jp: 返回排除同语种和粤语文言文的数组('jp'),
      ru: 返回排除同语种和粤语文言文的数组('ru'),
      kor: 返回排除同语种和粤语文言文的数组('kor'),
      de: 返回排除同语种和粤语文言文的数组('de'),
      fra: 返回排除同语种和粤语文言文的数组('fra'),
      it: 返回排除同语种和粤语文言文的数组('it'),
      pt: 返回排除同语种和粤语文言文的数组('pt'),
      th: 返回排除同语种和粤语文言文的数组('th'),
      spa: 返回排除同语种和粤语文言文的数组('spa'),
      cht: 返回排除同语种和粤语文言文的数组('cht'),
    },
    to不支持: undefined,
  },
  baidu: {
    from不支持: [],
    to不支持: [],
    自定义不支持: undefined,
  },
  tencent: {
    from不支持: 粤语和文言文,
    自定义不支持: {
      auto: 粤语和文言文,
      zh: 粤语和文言文,
      en: 粤语和文言文,
      jp: 从所有语种中排除(['zh', 'cht', 'en', 'kor']),
      ru: 从所有语种中排除(['zh', 'cht', 'en', 'fra', 'spa', 'it', 'de', 'pt']),
      kor: 从所有语种中排除(['zh', 'cht', 'en', 'jp']),
      de: 从所有语种中排除(['zh', 'cht', 'en', 'fra', 'spa', 'it', 'ru', 'pt']),
      fra: 从所有语种中排除(['zh', 'cht', 'en', 'spa', 'it', 'de', 'ru', 'pt']),
      pt: 从所有语种中排除(['zh', 'cht', 'en', 'fra', 'spa', 'it', 'de', 'ru']),
      spa: 从所有语种中排除(['zh', 'cht', 'en', 'fra', 'it', 'de', 'ru', 'pt']),
      it: 从所有语种中排除(['zh', 'cht', 'en', 'fra', 'spa', 'de', 'ru', 'pt']),
      th: 从所有语种中排除(['zh', 'cht', 'en']),
      cht: 粤语和文言文,
    },
    to不支持: undefined,
  },
  ali: {
    from不支持: ['wyw'],
    自定义不支持: {
      auto: 粤语和文言文,
      zh: ['wyw'],
      en: ['cht', 'wyw', 'yue'],
      jp: ['cht', 'wyw', 'yue'],
      ru: ['cht', 'wyw', 'yue'],
      kor: ['cht', 'wyw', 'yue'],
      de: ['cht', 'wyw', 'yue'],
      fra: ['cht', 'wyw', 'yue'],
      spa: ['cht', 'wyw', 'yue'],
      it: ['cht', 'wyw', 'yue'],
      pt: ['cht', 'wyw', 'yue'],
      th: ['cht', 'wyw', 'yue'],
      yue: 从所有语种中排除(['zh']),
      cht: 从所有语种中排除(['zh']),
    },
    to不支持: undefined,
  },
  youdao: {
    from不支持: ['wyw'],
    to不支持: ['wyw'],
    自定义不支持: undefined,
  },
  caiyun: {
    from不支持: 从所有语种中排除(['zh', 'en', 'jp', 'ru']),
    自定义不支持: {
      auto: 从所有语种中排除(['zh', 'en', 'jp', 'ru']),
      zh: 从所有语种中排除(['en', 'jp', 'ru']),
      en: 从所有语种中排除(['zh']),
      jp: 从所有语种中排除(['zh']),
      ru: 从所有语种中排除(['zh']),
    },
    to不支持: undefined,
  },
  huoshan: {
    from不支持: ['wyw'],
    to不支持: undefined,
    自定义不支持: {
      auto: 粤语和文言文,
      zh: 粤语和文言文,
      en: 粤语和文言文,
      jp: 粤语和文言文,
      ru: 粤语和文言文,
      kor: 粤语和文言文,
      de: 粤语和文言文,
      fra: 粤语和文言文,
      spa: 粤语和文言文,
      it: 粤语和文言文,
      pt: 粤语和文言文,
      th: 粤语和文言文,
      yue: 从所有语种中排除(['zh']),
      cht: 粤语和文言文,
    },
  },
  xiaoniu: {
    from不支持: ['wyw'],
    to不支持: ['wyw'],
    自定义不支持: undefined,
  },
  deepl: {
    from不支持: ['cht', 'yue', 'wyw', 'th'],
    to不支持: ['cht', 'yue', 'wyw', 'th'],
    自定义不支持: undefined,
  },
}
export interface 语种类型 {
  label: string
  value: string
  disabled: boolean
  id: string
  children?: 语种类型[]
}
const translateFromOptions: 语种类型[] = [
  { label: '自动检测', value: 'auto', disabled: false, id: nanoid() },
  { label: '最近', value: 'last', disabled: false, id: nanoid() },
  { label: '中文-简', value: 'zh', disabled: false, id: nanoid() },
  { label: '英语', value: 'en', disabled: false, id: nanoid() },
  { label: '日语', value: 'jp', disabled: false, id: nanoid() },
  { label: '俄语', value: 'ru', disabled: false, id: nanoid() },
  { label: '韩语', value: 'kor', disabled: false, id: nanoid() },
  { label: '德语', value: 'de', disabled: false, id: nanoid() },
  { label: '法语', value: 'fra', disabled: false, id: nanoid() },
  { label: '西班牙语', value: 'spa', disabled: false, id: nanoid() },
  { label: '意大利语', value: 'it', disabled: false, id: nanoid() },
  { label: '葡萄牙语', value: 'pt', disabled: false, id: nanoid() },
  { label: '泰语', value: 'th', disabled: false, id: nanoid() },
  { label: '粤语', value: 'yue', disabled: false, id: nanoid() },
  { label: '中文-繁', value: 'cht', disabled: false, id: nanoid() },
  { label: '文言文', value: 'wyw', disabled: false, id: nanoid() },
]

export function 返回语种树(arr = translateFromOptions) {
  const tmpArr = cloneDeep(arr)
  tmpArr.forEach((i) => {
    if (i.value !== 'last') {
      i.children = arr
        .filter(r => r.value !== 'auto' && r.value !== 'last')
        .map((j) => {
          return {
            ...j,
            id: nanoid(),
          }
        })
    }
    else {
      const lastArr = getDbStorageItem('lastFromTo', [])
      // 历史使用记录，单独处理
      i.children = lastArr.map((item: any) => {
        const [from, to] = item.split('>>')
        const fromLabel = 语种枚举[from]
        const toLabel = 语种枚举[to]
        const fromShortLabel = 语种简写映射[fromLabel] || fromLabel.substring(0, 2)
        const toShortLabel = 语种简写映射[toLabel] || toLabel.substring(0, 2)
        return {
          label: `${fromShortLabel} > ${toShortLabel}`,
          value: item,
          disabled: false,
          id: nanoid(),
        }
      })
    }
  })
  return tmpArr
}
