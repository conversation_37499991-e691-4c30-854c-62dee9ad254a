import { 获取推断语言名称, 语音标识修正 } from '@/utils/language'
import { debounce } from 'lodash-es'
import { 上传日志 } from '../log'

import {
  type 翻译参数Type,
  获取指定的翻译方法,
  读取并检查密钥配置,
  返回状态码及信息,
} from './common'
import { textAanalysis } from './serve/tencentTransmart'

const 防抖上传日志 = debounce((tag: string) => {
  上传日志(tag)
}, 5000)

/**
 *
 * @param tag 翻译标识
 * @param options 翻译参数
 * @returns
 */
export async function 通用翻译(tag: string, options: 翻译参数Type): Promise<any> {
  const { q, file } = options
  // let last
  // 空值优化
  if (!q && !file) {
    return 返回状态码及信息(400)
  }

  let { from, to } = options
  if (tag !== 'google') {
    // 因为谷歌翻译不同api的语言解析标识不一致，所以不统一做语言修正
    // 语言修正
    const temp = 语音标识修正(tag, options)
    from = temp.from
    to = temp.to
  }
  // 读取密钥信息
  const checkKey = 读取并检查密钥配置(tag)
  if (!checkKey.flag) {
    return 返回状态码及信息(401, undefined, checkKey.msg)
  }

  const keyConfig = checkKey.keyConfig

  const fn = 获取指定的翻译方法(tag)
  if (!fn) {
    return 返回状态码及信息(400, undefined, '这个功能还在建设中哦')
  }

  const result = await fn({ q, from, to, keyConfig, file })
  if (result.code === 503) {
    // 访问频率受限, 再次发起翻译
    return await 通用翻译(tag, options)
  }

  result.text = result.text?.trim() || ''
  result.tag = tag
  result.q = q

  if (result.code === 200) {
    防抖上传日志(tag)
  }

  return result
}

/**
 * 语言识别
 * 注：本语言识别功能由腾讯交互赞助
 */
export async function 语种识别(text: string) {
  const 语言Tag = await textAanalysis(text)

  const from = 获取推断语言名称('transmart', 语言Tag)
  return from
}
