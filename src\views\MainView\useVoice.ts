import type { 语种 } from '@/assets/translateApiOption'
/** 语音朗读相关业务 */
import { Message } from '@arco-design/web-vue'
import { 用户配置存储 } from '@MainView/MainViewData'
import { 上传日志 } from '@/apis/log'
import { 声音映射 } from '@/apis/mstts/data'
import { 语音朗读生成base64 } from '@/apis/mstts/index'

// 报错的相关资料：https://developer.chrome.com/blog/play-request-was-interrupted/
export type 级联值类型 = [语种 | 'auto' | 'last', 语种]

const 朗读性别偏好 = computed(() => {
  return 用户配置存储().baseConfig.readingPreference
})

export function 在线朗读主函数() {
  const 在线音频Url = ref('')
  const audioElement = document.createElement('audio')

  const audio本体 = ref<HTMLAudioElement>(audioElement)
  const { playing: 在线正在播放 } = useMediaControls(audio本体, { src: 在线音频Url })
  const 在线朗读loading = ref(false)

  function 在线朗读停止() {
    在线正在播放.value = false
    audio本体.value.autoplay = false
  }

  function 重置在线音频Url() {
    在线朗读停止()
    在线音频Url.value = ''
    audio本体.value.autoplay = true
  }

  async function 播放音频Browser(
    朗读文本: string,
    声音: string,
    语速: number,
    发音风格: string,
  ) {
    let result = false
    在线朗读loading.value = true

    try {
      const params = {
        voice: 声音,
        rate: 语速,
        text: 朗读文本,
        express: 发音风格,
      }

      const 原始文件流 = await 语音朗读生成base64(params)
      console.log('🚀 ~ 原始文件流 >>>', 原始文件流)

      if (!原始文件流?.size) {
        Message.error('语音生成失败，请检查网络连接或稍后重试')
        return false
      }

      if (原始文件流.type.includes('audio')) {
        在线音频Url.value = window.URL.createObjectURL(原始文件流)
        在线正在播放.value = true
        result = true
      }
      else {
        console.error('返回的文件类型不是音频:', 原始文件流.type)
        Message.error('返回的文件格式不正确，请重试')
      }
    }
    catch (error) {
      console.error('TTS播放出错:', error)
      if (error.message.includes('API请求失败')) {
        Message.error('TTS服务暂时不可用，请稍后重试')
      }
      else if (error.message.includes('网络')) {
        Message.error('网络连接失败，请检查网络设置')
      }
      else {
        Message.error('语音播放出错，请重试')
      }
    }
    finally {
      在线朗读loading.value = false
    }

    return result
  }

  async function 在线播放函数(朗读文本: string, 译文语言标识: 语种) {
    if (在线朗读loading.value) {
      Message.warning({ content: '加载音频中, 请稍等', duration: 1500 })
      return
    }
    重置在线音频Url()
    const 声音对象: any = 声音映射?.[译文语言标识] ?? 声音映射.zh!
    const 声音 = 声音对象[朗读性别偏好.value]?.ShortName ?? 'zh-CN-XiaoxiaoNeural'
    // 将rate转换为百分比形式，因为新API需要的是rate参数而不是speed
    // rate: 1.1 表示110%的语速，需要转换为10（即+10%）
    const 基础语速 = 声音对象?.rate ?? 1
    const 语速 = (基础语速 - 1) * 100 // 转换为百分比偏移量
    const 发音风格 = 声音对象[朗读性别偏好.value].express ?? 'general'

    // 现在统一使用Browser函数，因为新的TTS API是标准HTTP接口，不需要区分环境
    const 播放音频Fn = 播放音频Browser
    const 朗读结果 = await 播放音频Fn(朗读文本, 声音, 语速, 发音风格)
    朗读结果 && 上传日志('朗读')
  }

  return {
    在线朗读loading,
    在线播放函数,
    在线正在播放,
    在线音频Url,
    重置在线音频Url,
    在线朗读停止,
  }
}
