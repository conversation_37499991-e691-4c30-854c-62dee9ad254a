import type { 语种 } from '@/assets/translateApiOption'
import { 上传日志 } from '@/apis/log'
import { 声音映射 } from '@/apis/mstts/data'
import { 语音朗读生成ArrayBuffer, 语音朗读生成base64 } from '@/apis/mstts/index'
/** 语音朗读相关业务 */
import { Message } from '@arco-design/web-vue'
import { 用户配置存储 } from '@MainView/MainViewData'

// 报错的相关资料：https://developer.chrome.com/blog/play-request-was-interrupted/
export type 级联值类型 = [语种 | 'auto' | 'last', 语种]

const 朗读性别偏好 = computed(() => {
  return 用户配置存储().baseConfig.readingPreference
})

export function 在线朗读主函数() {
  const 在线音频Url = ref('')
  const audioElement = document.createElement('audio')

  const audio本体 = ref<HTMLAudioElement>(audioElement)
  const { playing: 在线正在播放 } = useMediaControls(audio本体, { src: 在线音频Url })
  const 在线朗读loading = ref(false)

  function 在线朗读停止() {
    在线正在播放.value = false
    audio本体.value.autoplay = false
  }

  function 重置在线音频Url() {
    在线朗读停止()
    在线音频Url.value = ''
    audio本体.value.autoplay = true
  }

  async function 播放音频Browser(
    朗读文本: string,
    声音: string,
    语速: number,
    发音风格: string,
  ) {
    let result = false
    在线朗读loading.value = true
    const params = {
      voice: 声音,
      rate: 语速,
      text: 朗读文本,
      express: 发音风格,
    }
    const 原始文件流 = await 语音朗读生成base64(params)
    console.log('🚀 ~ 原始文件流 >>>', 原始文件流)
    if (!原始文件流?.size) {
      Message.error('啊哦, 播放出错了')
      在线朗读loading.value = false
      return false
    }
    if (原始文件流.type.includes('audio')) {
      在线音频Url.value = window.URL.createObjectURL(原始文件流)

      在线正在播放.value = true
      result = true
    }
    else {
      Message.error('啊哦, 播放出错了, 请再试一次吧！')
    }
    在线朗读loading.value = false
    return result
  }

  async function 播放音频ByUtools(
    朗读文本: string,
    声音: string,
    语速: number,
    发音风格: string,
  ) {
    let result = false
    在线朗读loading.value = true
    const params = {
      voice: 声音,
      rate: 语速,
      text: 朗读文本,
      express: 发音风格,
    }
    try {
      const arrayBuffer = await 语音朗读生成ArrayBuffer(params)
      console.log('🚀 ~ arrayBuffer >>>', arrayBuffer)
      const file = new File([arrayBuffer], 'temp.mp3')
      console.log('🚀 ~ file >>>', file)
      if (!file?.size) {
        Message.error('啊哦, 播放出错了')
        在线朗读loading.value = false
        return
      }
      在线音频Url.value = window.URL.createObjectURL(file)
      在线正在播放.value = true
      result = true
    }
    catch (error) {
      Message.error('啊哦, 播放出错了')
    }
    在线朗读loading.value = false

    return result
  }

  async function 在线播放函数(朗读文本: string, 译文语言标识: 语种) {
    if (在线朗读loading.value) {
      Message.warning({ content: '加载音频中, 请稍等', duration: 1500 })
      return
    }
    重置在线音频Url()
    const 声音对象: any = 声音映射?.[译文语言标识] ?? 声音映射.zh!
    const 声音 = 声音对象[朗读性别偏好.value]?.ShortName ?? 'zh-CN-XiaoxiaoNeural'
    const 语速 = 声音对象?.rate ?? 1
    const 发音风格 = 声音对象[朗读性别偏好.value].express ?? 'general'

    // const 播放音频Fn = window.utools ? 播放音频V2 : 播放音频
    const 播放音频Fn = 播放音频Browser
    const 朗读结果 = await 播放音频Fn(朗读文本, 声音, 语速, 发音风格)
    朗读结果 && 上传日志('朗读')
  }

  return {
    在线朗读loading,
    在线播放函数,
    在线正在播放,
    在线音频Url,
    重置在线音频Url,
    在线朗读停止,
  }
}
