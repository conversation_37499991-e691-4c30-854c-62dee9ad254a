<template>
  <div class="relative grid-c h-screen overflow-hidden dark:(bg-#303133 text-white)">
    <div
      class="main size-full flex flex-col overflow-hidden px-21px pb-26px pt-6px"
    >
      <div
        bg="#fff dark:#242425a6"
        class="text_wrapper relative flex flex-1 rounded-t-8px"
      >
        <div class="absolute bottom-10px right-12px z-10 flex space-x-8px">
          <Transition name="component-scale" mode="out-in">
            <!-- 撤销按钮 -->
            <MimicryBtn v-if="撤销按钮计算显隐" key="undo" @click="撤销按钮()">
              <i i-lucide-undo />
            </MimicryBtn>

            <!-- 清除按钮 -->
            <MimicryBtn v-else-if="输入框有字" key="clear" @click="清空输入框()">
              <i i-line-md-close />
            </MimicryBtn>
          </Transition>
        </div>

        <Transition name="custom-fade" mode="out-in">
          <div
            v-show="检测语言显示条件"
            class="absolute bottom-10px left-12px z-10 flex items-end"
          >
            <ReadBtn
              ref="原文朗读按钮Ref"
              :code="结果对象.from语种?.tag ? 200 : 666"
              :text="用户输入"
              :language-type="
                form和to的数组[0] === 'auto' ? 结果对象.from语种?.tag : form和to的数组[0]
              "
              btn-type="原文"
              @start-reading-online="批量暂停在线朗读([$event])"
              @start-read-offline="批量停止离线朗读([$event])"
            />
            <div
              class="absolute select-none whitespace-nowrap transition-cusbezier-250"
              :style="{ left: 语言检测Left }"
              text="12px #ccc dark:#fff/35"
            >
              检测到: {{ 结果对象.from语种?.languageText }}
            </div>
          </div>
        </Transition>

        <!-- 上方文本域 -->
        <a-textarea
          ref="用户输入框Ref"
          v-model="用户输入"
          class="rounded-t-8px"
          :class="{ jp_font: 结果对象?.from语种?.tag === 'jp' }"
          :max-length="当前需要限制字符 ? 限制的字符数量 : Infinity"
          :show-word-limit="当前需要限制字符"
          :placeholder="输入框placeholder"
          @click.right="右键点击文本域($event)"
          @focus="文本框聚焦 = true"
          @blur="文本框聚焦 = false"
        />
      </div>
      <section class="tools_wrapper my-8px flex">
        <!-- 中间翻译Api选项 -->
        <SegmentedGroup
          ref="SegmentedRef"
          v-model="当前翻译api"
          class="!rounded-b-none"
          @update:model-value="切换翻译服务"
        >
          <SegmentedItem v-for="item in 翻译api数组" :key="item.value" :value="item.value">
            <div class="flex flex-nowrap select-none space-x-3px">
              <div
                v-show="显示api图标条件(item)"
                class="grid-c will-change-filter transition-cusbezier-250"
                :class="[当前翻译api === item.value ? 'contrast-130' : 'grayscale-20 ']"
              >
                <ApiIcon :data="item" :current="当前翻译api" />
              </div>
              <div
                class="flex-c flex-1 whitespace-nowrap transition-cusbezier-200"
                :class="[当前翻译api === item.value && 'I_text-shadow-[0_3px_15px_#ffffffb8] text-primary dark:text-#fff']"
              >
                {{ item.label.slice(0, 服务名字数) }}
              </div>
            </div>
          </SegmentedItem>
        </SegmentedGroup>
        <div
          border="#f2f3f5 b-width-1px dark:#3d3d3d"
          class="flex-1 justify-end horizontal-place-4px"
        >
          <template v-if="!是命名模式">
            <AutoBtn :active="自动模式" @click="节流切换自动模式()" />
            <a-cascader
              v-model:model-value="form和to的数组"
              :path-mode="true"
              :options="语种树的数据"
              :style="{
                width: '240px',
                height: '33px',
                borderRadius: '3px 3px 0 0',
              }"
              value-key="id"
              :format-label="格式化级联显示内容"
              @change="语种级联发生变化()"
            />
          </template>

          <!-- 命名翻译模式的select -->
          <template v-else>
            <a-select
              v-model="命名模式类型"
              :style="{ width: '140px' }"
              @change="结果对象.结果文字 = 获取命名模式对应结果()"
            >
              <a-option
                v-for="(项, 索引) in 已启用的切换类型数组"
                :key="索引"
                :value="项.name"
              >
                {{ 项.label }}
              </a-option>
            </a-select>
          </template>
        </div>
      </section>

      <a-resize-box
        :key="伸缩框的key"
        :directions="['top']"
        :style="{
          minHeight: '22%',
          maxHeight: '78%',
          height: 'calc(50% - 21.5px)',
        }"
        @moving-end="文本框被拖拽 = true"
      >
        <div
          bg="#fff dark:#242425a6"
          class="relative h-full flex"
        >
          <!-- -1：等待用户操作、200：翻译成功均应该显示<code/> -->
          <Transition name="custom-fade" mode="out-in">
            <CodeBg v-if="是命名模式 && [-1, 200].includes(结果对象.状态码)" />
          </Transition>
          <Transition name="custom-fade" mode="out-in">
            <Loading
              v-if="翻译加载"
              border="~ #e9e9e9"
              class="absolute top-0 z-100 rounded-b-8px"
            />
          </Transition>
          <div
            class="text_wrapper text_readonly absolute top-0 size-full flex flex-1"
            :class="{
              code_font: 是命名模式,
              jp_font: form和to的数组[1] === 'jp',
            }"
          >
            <a-textarea
              v-model="结果对象.结果文字"
              rounded="b-8px t-0"
              class="relative z-1"
              :placeholder="是命名模式 ? 命名placeholder : 结果框placeholder"
              :readonly="结果只读"
            />

            <!-- 译文朗读按钮 -->
            <div class="absolute bottom-10px left-12px">
              <ReadBtn
                v-if="结果只读"
                ref="译文朗读按钮Ref"
                :code="结果对象.状态码"
                :text="结果对象.结果文字"
                :language-type="form和to的数组[1]"
                btn-type="译文"
                @start-reading-online="批量暂停在线朗读([$event])"
                @start-read-offline="批量停止离线朗读([$event])"
              />
            </div>

            <!-- 手动翻译按钮 -->
            <div ref="右下按钮包裹Ref" class="absolute bottom-10px right-12px z-101">
              <CusTooltip
                content="按回车(Enter)也可以"
                position="left"
                :popup-container="右下按钮包裹Ref"
              >
                <Transition name="component-scale" mode="out-in">
                  <!-- 手动翻译按钮 -->
                  <MimicryBtn
                    v-if="输入框有字 && 需要手动翻译"
                    @click="处理用户输入给出结果({ 节流: true })"
                  >
                    <i class="i-cus-yi" />
                  </MimicryBtn>
                </Transition>
              </CusTooltip>
            </div>

            <Transition name="custom-fade" mode="out-in">
              <div
                v-show="要显示复制按钮"
                class="absolute-x-center bottom-10px z-1 flex space-x-8px"
              >
                <ColorfulBtn
                  v-if="baseConfig.copyBtnShow.includes(1)"
                  icon-name="i-line-md-clipboard-arrow"
                  @click="点击触发复制主函数('open')"
                >
                  仅复制
                </ColorfulBtn>

                <ColorfulBtn
                  v-if="baseConfig.copyBtnShow.includes(2)"
                  icon-name="i-line-md-navigation-right-down"
                  @click="点击触发复制主函数('close')"
                >
                  复制并隐藏
                </ColorfulBtn>

                <ColorfulBtn
                  v-if="baseConfig.copyBtnShow.includes(3)"
                  icon-name="i-line-md-edit-twotone"
                  @click="点击触发复制主函数('closeInput')"
                >
                  复制并输入
                </ColorfulBtn>
              </div>
            </Transition>
          </div>
        </div>
      </a-resize-box>
    </div>
    <div class="absolute bottom-0px h-26px w-full flex justify-between px-4px">
      <!-- 命名翻译模式按钮 -->
      <button
        class="icon"
        :class="[是命名模式 ? '!text-primary i-tabler-code' : 'i-tabler-code-off ']"
        @click="开关命名模式()"
        @click.right.prevent="打开命名模式设置弹窗()"
      />
      <!-- 底栏 -->
      <div
        v-if="底栏显示条件"
        transition="all-200 delay-100 hover:!delay-0"
        position="relative z-150"
        class="tools_bar_wrapper flex-1 -bottom-[calc(100%+1px)] hover:bottom-0"
      >
        <ToolsBar
          v-model:result-readonly="结果只读"
          :显示重置伸缩框按钮="文本框被拖拽"
          :命名模式="是命名模式"
          @换行的值变了="开始翻译()"
          @需要停止音频="停止所有音频播放({ 重置译文: true, 重置原文: true })"
          @重置文本框高度="重置下方文本框()"
          @需要focus="输入框focus()"
          @打开图片翻译="图片翻译ref.打开model()"
        />
      </div>

      <!-- 设置按钮 -->
      <SmoothTransitionIcon
        id="setting-wrapper"
        class="icon hover:rotate-90"
        默认图标class="i-ci-settings-future"
        hover时候的class="i-eos-icons-rotating-gear "
        渲染标签="button"
        @click="打开设置Modal()"
      />
    </div>
  </div>

  <!-- 设置弹窗 -->
  <SettingModal
    ref="设置弹框Ref"
    @ok="设置确定($event)"
    @cancel="设置取消()"
    @reset="resetHandler()"
    @open-name-setting="打开命名模式设置弹窗()"
    @更新语法树="更新语法树()"
  />

  <CodeModeSetting ref="命名模式设置ref" />
  <ImageTranslate ref="图片翻译ref" @go-baidu="图片翻译去设置页()" />
</template>

<script setup lang="ts">
import type { 朗读类型 } from '@/types'
import type { 级联值类型, 翻译参数Type } from '@MainView/MainViewTypes'
import { api选项 } from '@/components/SettingModal/SettingsData'
import { useGlobalStore } from '@/components/SettingModal/SettingsModules'
import { 添加历史记录 } from '@/utils/lastLan'
import { getDbStorageItem, setDbStorageItem } from '@/utils/storage'
import CodeModeSetting from '@/views/CodeModeSetting.vue'
import ImageTranslate from '@/views/ImageTranslate.vue'
import { Message } from '@arco-design/web-vue'

import { api不支持的大对象, 用户配置存储, 返回语种树 } from '@MainView/MainViewData'
import {
  useUtools,
  use命名模式模块,
  复制主函数,
  未配置服务引导,
  格式化级联显示内容,
  检查from和to是否兼容,
  用库推测语种,
  用抽样推测语种,
  语种识别,
  通用翻译,
  首次引导,
} from '@MainView/MainViewModule'
import { 粘贴, 获取存储项 } from '@MainView/MainViewUtils'
import { sentenceCase } from 'change-case'

import dayjs from 'dayjs'
import { debounce, throttle } from 'lodash-es'
import { nanoid } from 'nanoid'
import '@MainView/useShortcutKey'

defineOptions({
  name: 'MainView',
})
const 全局存储 = useGlobalStore()
const 语种树的数据 = ref(返回语种树())

const form和to的数组 = ref<级联值类型>(['auto', 'zh'])
const 用户配置 = 用户配置存储()
const baseConfig = computed(() => 用户配置.baseConfig)

const 文字尺寸 = computed(() => `${baseConfig.value.fontSize}px`)
const 翻译api数组 = computed(() => 用户配置.getHomeApiOptions)

const 翻译加载 = ref(false) // 是否正在翻译
const 用户输入 = ref('') // 输入的内容
const 结果对象语种: {
  tag: any
  languageText: string
} = {
  tag: undefined,
  languageText: '',
}
const 结果对象原始数据 = {
  结果文字: '', // 翻译结果
  状态码: -1, // 翻译结果状态(code = 200 为成功,code = -1为等待用户操作,code = 401为未配置翻译API)
  from语种: 结果对象语种,
  结果编号: nanoid(),
}
const 结果对象 = reactive({ ...结果对象原始数据 })
const 当前翻译api = ref('') // 当前翻译api
const 设置弹框Ref = ref() // 设置弹窗的ref
const 用户输入框Ref = ref() // 输入textarea的ref
const 右下按钮包裹Ref = ref() // 结果框右下角的包裹dom的ref

const {
  是命名模式,
  命名模式类型,
  已启用的切换类型数组,
  获取命名模式对应结果,
  返回命名模式对应结果,
  改变命名模式类型,
} = use命名模式模块(打开命名模式设置弹窗)

const 图片翻译ref = ref()

const { utools初始化 } = useUtools(
  设置弹框Ref,
  用户输入,
  改变命名模式类型,
  图片翻译ref,
  // (imgUrl?: string) => {
  //   图片翻译ref.value.打开model(imgUrl)
  // },
)

const 要显示复制按钮 = computed(() => {
  return !!结果对象.结果文字?.trim() && 结果对象.状态码 === 200
})
const { width: 屏幕宽度 } = useWindowSize()
const 惰性屏宽 = refThrottled(屏幕宽度, 300)
/**
 * @param map 服务个数与断点的Map
 * @param 服务个数 当前服务个数
 */
function 获取断点Map的Value(map: Map<number, number>, 服务个数: number) {
  const keys = Array.from(map.keys())
  const 最小断点的key = Math.min(...keys)
  const 最大断点的key = Math.max(...keys)
  if (服务个数 < 最小断点的key) {
    return -1
  }
  else if (服务个数 > 最大断点的key) {
    return Number.POSITIVE_INFINITY
  }
  else {
    return map.get(服务个数) ?? Number.POSITIVE_INFINITY
  }
}

function 显示api图标条件(item: any) {
  // 不同的服务个数, 显示api图标的条件不同
  // 服务个数为1、2、3、4、5时, 一定会显示所有的api图标, 窗口宽度一定大于-1 (这是绝对的)
  // 为6时, 窗口宽度大于等于822时, 一定会显示所有的图标
  // 为7时, 窗口宽度大于等于885时, 一定会显示所有的图标
  // 否则, 只显示当前选中的服务项图标
  const 服务个数与断点Map = new Map([
    [6, 822],
    [7, 885],
  ])
  const 翻译api数组len = 翻译api数组.value.length
  const 选到自己了 = item.value === 当前翻译api.value
  return (
    选到自己了 || 惰性屏宽.value > 获取断点Map的Value(服务个数与断点Map, 翻译api数组len)
  )
}

const 服务名字数 = computed(() => {
  // 不同的服务个数, 显示4字切还到2字的条件不同
  // 服务个数为1、2、3、4时, 一定会显示4字, 除非窗口宽度小于-1 (这当然不可能)
  // 为5时, 窗口宽度小于等于885时, 会显示2字
  // 为6时, 窗口宽度小于等于992时, 会显示2字
  // 为7时, 窗口宽度小于等于1090时, 会显示2字
  const 服务个数与断点Map = new Map([
    [5, 885],
    [6, 992],
    [7, 1090],
  ])
  const 翻译api数组len = 翻译api数组.value.length
  const 需要的文字数量 = 翻译api数组len > 4 && 惰性屏宽.value < 获取断点Map的Value(服务个数与断点Map, 翻译api数组len) ? 2 : 5
  return 需要的文字数量
})
const { history: 输入历史, undo: 撤销 } = useRefHistory(用户输入, {
  // Chrome内核98+可用structuredClone
  dump: structuredClone,
  capacity: 4,
})
const 撤销按钮Vis = ref(false)
const 撤销按钮计算显隐 = computed(() => {
  const 最后一条历史文字 = 输入历史.value?.[1]?.snapshot as string
  return 撤销按钮Vis.value && !!最后一条历史文字?.trim()
})
const 最新翻译请求ID = ref('')
function 重置翻译状态() {
  结果对象.结果文字 = ''
  结果对象.状态码 = -1
  结果对象.from语种 = { tag: '', languageText: '' }
  翻译加载.value = false
  最新翻译请求ID.value = ''
}

const 输入框有字 = computed(() => {
  return !['', undefined, null].includes(用户输入.value)
})

// 小牛的文本限制字符距离右边的距离
const text_limit_right = computed(() => {
  return [撤销按钮计算显隐.value, 输入框有字.value].some(Boolean) ? '42px' : '10px'
})

const 限制的字符数量 = computed(() => {
  return 当前翻译api.value === 'google' ? 4000 : Number.POSITIVE_INFINITY
})

const 当前需要限制字符 = computed(() => {
  return 当前翻译api.value === 'google'
})

/**
 * 处理白嫖情况，如果用户在白嫖，且输入的字数大于可白嫖的字数，则提示用户，并截取允许的白嫖的字数
 * @param str 要处理的字符串
 */
function 处理白嫖情况(str: string) {
  const 当前翻译服务商名 = api选项.find(i => i.value === 当前翻译api.value)?.label || ''
  if (当前需要限制字符.value && 用户输入.value?.length >= 限制的字符数量.value) {
    Message.warning(`${当前翻译服务商名}将只翻译前${限制的字符数量.value}个字`)
    return str.slice(0, 限制的字符数量.value)
  }
  return str
}

function 清空输入框() {
  用户输入.value = ''
  撤销按钮Vis.value = true
  输入框focus()
}

function 撤销按钮() {
  撤销()
  撤销按钮Vis.value = false
}

function 右键点击文本域(e: MouseEvent) {
  e.preventDefault()
  输入框focus()
  粘贴()
}

const 检测语言显示条件 = computed(() => {
  return !!结果对象.from语种?.languageText
})

function 输入框focus() {
  用户输入框Ref.value.focus()
}
const 分段控制器Ref = useTemplateRef('SegmentedRef')

// 设置弹框点击了确定
function 设置确定(传过来的消息: any) {
  const { 是否改动 = true } = 传过来的消息
  nextTick(() => {
    读取设置()
    输入框focus()
    分段控制器Ref.value?.updateIndicatorPosition()
    if (是否改动) {
      // 设置成功, 刷新上一次翻译
      开始翻译(当前翻译api.value)
    }
  })
}

// 设置弹框点击了取消
function 设置取消() {
  输入框focus()
}

// 打开设置模态框
function 打开设置Modal() {
  停止所有音频播放()
  设置弹框Ref.value.打开弹窗()
}

const 自动模式 = ref(true)
const 切换自动模式 = useToggle(自动模式)
const 节流切换自动模式 = throttle(() => {
  切换自动模式()
}, 300)

watch(自动模式, (newVal) => {
  const 提示语 = newVal ? '自动切换语种已开启' : '自动切换语种已关闭'
  const 类型 = newVal ? 'success' : 'error'
  switchMessage(类型, 提示语, 1000)
  输入框focus()
  if (newVal) {
    开始翻译()
  }
})

// 变更模式
const 开关命名模式 = throttle(() => {
  const 类型 = 是命名模式.value ? 'error' : 'success'
  switchMessage(类型, `命名翻译模式${是命名模式.value ? '关闭' : '开启'}`)
  // 如果未输入, 则状态码设为-1, 即为等待用户操作状态, -1会触发Code动画
  // 否则, 将状态码设为0, 后面会触发翻译, 翻译成功后继而变为200, 会在成功后触发Code动画
  // 如果连续翻译, 状态码从200 => 200并不会触发Code动画, 符合预期
  结果对象.状态码 = !用户输入.value ? -1 : 0

  用户配置.saveBaseConfigByKey('codeMode', !是命名模式.value)
  输入框focus()
  setTimeout(() => {
    处理用户输入给出结果()
  }, 0)
}, 1000)

// 修改翻译服务, 同时保存当前选中的服务作为默认, 并翻译
function 切换翻译服务() {
  用户配置.saveBaseConfigByKey('defaultApi', 当前翻译api.value)
  重置翻译状态()
  处理用户输入给出结果()
}

// 命名模式在其他情况下变更了
watch(命名模式类型, () => {
  处理用户输入给出结果()
})

function 点击触发复制主函数(type: 'open' | 'close' | 'closeInput') {
  复制主函数('手动', 结果对象.结果文字, type)
}

function 是否仅包含大小写数字符号(s: string): boolean {
  if (!s) {
    return false
  }
  const pattern = /^[\w\-+=`~()|!@#$%^&*<>[\]{}\\ ./\r\n]+$/
  return pattern.test(s.trim())
}

const 处于命名且不用翻译 = computed(() => {
  return 是命名模式.value && 是否仅包含大小写数字符号(用户输入.value)
})

const 需要手动翻译 = computed(() => {
  return baseConfig.value.translationTriggerMode === 'manual'
})

// 这里获取到的 from 和to 包含了 last 选项后取到的实际值
function 获取实际的from和to() {
  const [fromSelect, toSelect] = form和to的数组.value

  let from = fromSelect
  let to = toSelect
  if (fromSelect === 'last') {
    // 最近使用，取到真正的from和to
    const [lastFrom, lastTo] = toSelect.split('>>')
    from = lastFrom
    to = lastTo
  }

  return [from, to]
}

async function 开始翻译(val = 当前翻译api.value) {
  停止所有音频播放({ 重置译文: true, 重置原文: true })
  输入框focus()
  // 如果没输入内容, 则不翻译
  if ([undefined, null, ''].includes(用户输入.value.trim())) {
    结果对象.结果文字 = ''
    return
  }

  if (自动模式.value && !是命名模式.value) {
    重置from和to为推测值()
  }

  const [from, to] = 获取实际的from和to()

  const [fromSelect] = form和to的数组.value
  if (fromSelect !== 'last') {
    添加历史记录(from, to)
    更新语法树()
  }

  const 能发请求翻译的文字 = 处理白嫖情况(用户输入.value)
  const 用户输入的文字 = baseConfig.value.removeN
    ? 去除换行(能发请求翻译的文字)
    : 能发请求翻译的文字

  const obj = {
    from,
    to,
    q: 尝试分词(用户输入的文字),
  }
  if (全局存储.用户未输入) {
    return
  }
  try {
    const { text: 返回的文字, code, from: from语种 } = await 调用翻译Api(val, obj)
    const 返回数据 = {
      from语种,
      结果文字: 是命名模式.value
        ? 返回命名模式对应结果(返回的文字, 命名模式类型.value)
        : 返回的文字,
      状态码: code,
      结果编号: nanoid(),
    }
    Object.assign(结果对象, 返回数据)
    if (code === 200) {
      const 识别到的语种对象 = await 语种识别(用户输入的文字)
      结果对象.from语种 = 识别到的语种对象
    }
  }
  catch {}
}

async function 调用翻译Api(tag: string, options: 翻译参数Type) {
  const 当前请求ID = nanoid()
  最新翻译请求ID.value = 当前请求ID

  翻译加载.value = true

  try {
    const 结果 = await 通用翻译(tag, options)

    // 检查这个请求是否是最新的
    if (最新翻译请求ID.value === 当前请求ID) {
      翻译加载.value = false

      if (!用户输入.value.trim()) {
        重置翻译状态()
        return { text: '', from: undefined, code: -1 }
      }

      return 结果
    }
  }
  catch {
    if (最新翻译请求ID.value === 当前请求ID) {
      翻译加载.value = false
      重置翻译状态()
    }
  }

  // 如果不是最新的翻译结果，返回一个空结果
  return { text: '', from: undefined, code: 0 }
}

function 尝试分词(str: string) {
  const reg = /^[A-Z-_]+\d*$/gi
  const result = reg.test(str)
  return result ? sentenceCase(str) : str
}

function 去除换行(str: string) {
  return str.replace(/[\r\n]/g, ' ').trim()
}

function 语种级联发生变化() {
  自动模式.value = false
  输入框focus()
  setTimeout(() => {
    开始翻译()
  }, 0)
}

function 读取设置() {
  //  首次加载设置当前选中为设置的默认翻译
  if (!baseConfig.value.homeOption.includes(当前翻译api.value)) {
    当前翻译api.value = 用户配置.baseConfig.defaultApi || baseConfig.value.homeOption?.[0]
  }
}

// 重置后首页设置
function resetHandler() {
  清空输入框()
  读取设置()
}

function 重置from和to为固定值(arr: 级联值类型 = ['auto', 'zh']) {
  form和to的数组.value = arr
}

function 重置from和to为推测值(str = 用户输入.value) {
  const 目标外语 = baseConfig.value.defaultForeignLanguage
  const 当前翻译服务支持的输入的List = new Set(语种树的数据.value?.map(i => i.value).filter(i => i !== 'auto'))

  const 库推测语种结果 = 用库推测语种(str)

  let 推测结果
  if (当前翻译服务支持的输入的List.has(库推测语种结果)) {
    推测结果 = 库推测语种结果
  }
  else {
    推测结果 = 用抽样推测语种(str)
  }
  const 推测结果是中文 = 推测结果 === 'zh'
  const 级联第2个值 = 推测结果是中文 ? 目标外语 : 'zh'
  form和to的数组.value = ['auto', 级联第2个值]
}

function 根据自动模式重置from和to() {
  if (自动模式.value) {
    重置from和to为推测值()
  }
  else {
    重置from和to为固定值()
  }
}

const 设置弹框正在活动 = computed(() => 设置弹框Ref.value.modal可见) // 设置弹窗的显隐状态
// 监听401, 自动弹引导层
watch(
  () => 结果对象.结果编号,
  () => {
    if (结果对象.状态码 === 401 && !设置弹框正在活动.value) {
      未配置服务引导()
    }
  },
)

function 根据手动翻译状态拼接文案(str = '') {
  return 需要手动翻译.value ? `${str}，Enter键开始翻译` : str
}

const 输入框placeholder = computed(() => {
  const 默认提示语 = '请输入要翻译的内容，可右键点击文本框粘贴'
  return 需要手动翻译.value ? `${默认提示语}，Shift+Enter换行` : 默认提示语
})

const 结果框placeholder = computed(() => {
  return 根据手动翻译状态拼接文案('翻译结果')
})

const 命名placeholder = computed(() => {
  const 命名模式的提示 = 已启用的切换类型数组.value.find(
    item => item.name === 命名模式类型.value,
  )?.remark
  return 根据手动翻译状态拼接文案(命名模式的提示)
})

// 页面可见性逻辑
const 页面可见性 = useDocumentVisibility()
watch(页面可见性, (current, previous) => {
  if (current === 'visible' && previous === 'hidden') {
    输入框focus()
  }
  else if (current === 'hidden' && previous === 'visible') {
    停止所有音频播放()
  }
})

// 加了防抖的开始翻译
const 防抖翻译 = debounce(() => {
  开始翻译()
}, 350)

const 节流翻译 = throttle(() => {
  开始翻译()
}, 1500)

// 监听用户
watch(用户输入, (newVal) => {
  if (!newVal.trim()) {
    // 如果用户输入空了
    重置翻译状态()
  }
  else {
    撤销按钮Vis.value = false
  }
  if (!需要手动翻译.value) {
    处理用户输入给出结果({ 防抖: true })
  }
})

interface 开始翻译参数类型 {
  防抖?: boolean
  节流?: boolean
}

function 处理用户输入给出结果(params: 开始翻译参数类型 = {}) {
  const { 防抖 = false, 节流 = false } = params
  if (处于命名且不用翻译.value) {
    用户输入处理为对应的命名模式()
  }
  else {
    if (防抖) {
      防抖翻译()
    }
    else if (节流) {
      节流翻译()
    }
    else {
      开始翻译()
    }
  }
}

function 用户输入处理为对应的命名模式() {
  结果对象.结果文字 = 返回命名模式对应结果(用户输入.value, 命名模式类型.value)
  结果对象.结果编号 = nanoid()
  结果对象.状态码 = 200
}

// 语种级联禁用逻辑
watchEffect(() => {
  const 当前api规则 = api不支持的大对象?.[当前翻译api.value]
  if (!当前api规则) {
    return
  }
  const 非互翻_自定义不支持: any = 当前api规则?.自定义不支持 // 不支持互翻的才会有这个obj
  const 互翻_to不支持的数组 = 当前api规则?.to不支持 // 支持互翻的会有这个数组

  语种树的数据.value.forEach((源语言项) => {
    // 非最近使用时判断源语言
    if (源语言项.value !== 'last') {
      // 一层循环禁用掉api本身就不支持的语种
      源语言项.disabled = 当前api规则?.from不支持.includes(源语言项.value)

      // 如果存在「自定义不支持」这个对象, 则为不支持任意互翻api, 根据数据禁用对应的不支持互翻的语种
      if (非互翻_自定义不支持) {
        ;(源语言项.children || []).forEach((目标语言项) => {
          const 不支持的数组 = 非互翻_自定义不支持[源语言项.value]
          目标语言项.disabled = 不支持的数组
            ? 不支持的数组.includes(目标语言项.value)
            : true
        })
      }
      else if (互翻_to不支持的数组) {
        // 如果存在"互翻_to不支持的数组", 则代表该api支持任意互翻, 禁用掉本就不支持的语种即可
        ;(源语言项.children || []).forEach((目标语言项) => {
          目标语言项.disabled = 互翻_to不支持的数组.includes(目标语言项.value)
        })
      }
    }
    else {
      // 最近使用的语法树禁用关系
      // 禁用不符合当前翻译 api 的方向
      (源语言项.children || []).forEach((item) => {
        const [from, to] = item.value.split('>>')
        item.disabled = false

        if (非互翻_自定义不支持) {
          const 不支持的数组 = 非互翻_自定义不支持[from]
          if (不支持的数组 && 不支持的数组.includes(to)) {
            item.disabled = true
          }
        }
        else if (互翻_to不支持的数组) {
          if (互翻_to不支持的数组.includes(to)) {
            item.disabled = true
          }
        }
      })
    }
  })
})

// 监听当前的to和form是否是当前api不支持的, 如果存在不支持的, 则重置
watchEffect(() => {
  if (!当前翻译api.value) {
    return
  }

  const formTo数组 = 获取实际的from和to()

  const 兼容结果 = 检查from和to是否兼容(formTo数组, 当前翻译api.value)

  // 如果不兼容, 则自动重置
  if (兼容结果 !== '兼容') {
    根据自动模式重置from和to()
  }
})

watchEffect(() => {
  全局存储.存储原文译文标识(form和to的数组.value) // 在级联改变时进行存储
})
watchEffect(() => {
  全局存储.存储翻译结果(结果对象.结果文字) // 结果文字改变时进行存储
})
watchEffect(() => {
  全局存储.设置当前翻译服务(当前翻译api.value) // 翻译服务改变时进行存储
})
watchEffect(() => {
  全局存储.设置当前翻译状态码(结果对象.状态码) // 翻译状态码改变时进行存储
})
watchEffect(() => {
  全局存储.存储底部按钮组显示状态(要显示复制按钮.value) // 共享此状态给复制的快捷键判断是否需要响应
})

const 结果只读 = ref(true) // 结果是否可为只读状态

watch(
  是命名模式,
  (newVal) => {
    if (newVal) {
      form和to的数组.value = ['auto', 'en']
      结果只读.value = true
    }
    else {
      根据自动模式重置from和to()
    }
  },
  { immediate: true },
)

function 获取下一个api的value() {
  let 下一个api的index = 翻译api数组.value.findIndex(i => i.value === 当前翻译api.value) + 1
  if (下一个api的index >= 翻译api数组.value.length) {
    下一个api的index = 0
  }
  return 翻译api数组.value[下一个api的index]?.value
}

// Tab键切换翻译服务
onKeyStroke(
  'Tab',
  (e) => {
    e.preventDefault()
    if (设置弹框正在活动.value || 翻译api数组.value.length <= 1) {
      return
    }
    当前翻译api.value = 获取下一个api的value()
    setTimeout(() => {
      处理用户输入给出结果({ 防抖: true })
    }, 0)
  },
  { dedupe: false },
)

const { Shift } = useMagicKeys()
// 需要手动翻译时, 回车键开始翻译
onKeyStroke(
  'Enter',
  (e) => {
    // 如果按Enter的时候，shift也是按着的
    if (Shift.value) {
      // 在自动翻译模式下（监听输入），Shift+Enter是不应该有任何作用的，所以阻止默认行为
      if (!需要手动翻译.value) {
        e.preventDefault()
      }
      // 在手动翻译模式下，Shift+Enter是换行，因为Enter默认就是换行，所以不继续执行后面的代码就好
      return
    }

    // 手动按Enter才触发翻译的模式下
    if (需要手动翻译.value) {
      // 按Enter就不要换行了，上面处理了，手动Enter触发的情况下，Shift+Enter是换行
      e.preventDefault()
      处理用户输入给出结果({ 节流: true })
    }
  },
  { dedupe: true },
)

const 退格3击时间 = 250 // 退格3击的时间不能超过这个值, 超过则不算3击
const 第一次按退格的时间戳 = refAutoReset(0, 退格3击时间) // 退格键的时间戳
const 退格次数 = refAutoReset(0, 退格3击时间) // 退格键的按键次数
const 文本框聚焦 = ref(false) // 文本框是否聚焦

onKeyStroke(
  'Backspace',
  () => {
    // 没开这功能 或者 文本框没聚焦
    if (!baseConfig.value.tripleBackspaceClear || !文本框聚焦.value) {
      return
    }
    if (第一次按退格的时间戳.value === 0) {
      第一次按退格的时间戳.value = dayjs().valueOf()
    }
    退格次数.value += 1
    if (
      退格次数.value >= 3
      && dayjs().valueOf() - 第一次按退格的时间戳.value <= 退格3击时间
    ) {
      退格次数.value = 0
      第一次按退格的时间戳.value = 0
      清空输入框()
    }
  },
  { dedupe: true },
)
const 原文朗读按钮Ref = ref()
const 译文朗读按钮Ref = ref()

interface 重置类型 {
  重置原文?: boolean
  重置译文?: boolean
}

function 停止所有音频播放(params: 重置类型 = {}) {
  const { 重置原文 = false, 重置译文 = false } = params
  // 重置译文 && 译文朗读按钮Ref.value?.重置在线音频Url()
  if (重置译文) {
    译文朗读按钮Ref.value?.重置在线音频Url()
  }
  if (重置原文) {
    原文朗读按钮Ref.value?.重置在线音频Url()
  }
  批量暂停在线朗读(['原文', '译文'])
  批量停止离线朗读(['原文', '译文'])
}

function 批量暂停在线朗读(typeArr: 朗读类型[] = []) {
  typeArr.forEach((type: 朗读类型) => {
    暂停单个在线朗读(type)
  })
}

function 批量停止离线朗读(typeArr: 朗读类型[] = []) {
  typeArr.forEach((type: 朗读类型) => {
    停止单个离线朗读(type)
  })
}

function 停止单个离线朗读(type: 朗读类型) {
  switch (type) {
    case '原文':
      if (译文朗读按钮Ref.value) {
        译文朗读按钮Ref.value.离线朗读状态 = 'end'
        译文朗读按钮Ref.value.离线朗读停止()
      }
      break
    case '译文':
      if (原文朗读按钮Ref.value) {
        原文朗读按钮Ref.value.离线朗读状态 = 'end'
        原文朗读按钮Ref.value.离线朗读停止()
      }
      break
  }
}

function 暂停单个在线朗读(type: 朗读类型) {
  switch (type) {
    case '原文':
      译文朗读按钮Ref.value?.在线朗读停止()
      break
    case '译文':
      原文朗读按钮Ref.value?.在线朗读停止()
      break
  }
}

const 伸缩框的key = ref(nanoid())
const 文本框被拖拽 = ref(false)

function 重置下方文本框() {
  文本框被拖拽.value = false
  伸缩框的key.value = nanoid()
}

const 底栏显示条件 = ref(false)
function 延迟显示底栏() {
  setTimeout(() => {
    底栏显示条件.value = true
  }, 500)
}

const 命名模式设置ref = ref()
function 打开命名模式设置弹窗() {
  命名模式设置ref.value.打开model()
}

const 原文朗读按钮个数 = computed(() => {
  return 原文朗读按钮Ref.value?.显示的按钮数量 || 0
})

const 语言检测Left = computed(() => {
  const m = new Map([
    [0, 0],
    [1, 31],
    [2, 62],
  ])
  return `${m.get(原文朗读按钮个数.value) || 0}px`
})

function 图片翻译去设置页() {
  设置弹框Ref.value.打开弹窗('baidu')
}

function 更新语法树() {
  语种树的数据.value = 返回语种树()
}

onMounted(() => {
  // 打开命名模式设置()
  utools初始化()
  输入框focus()
  读取设置()
  if (!获取存储项('firstUseMain')) {
    首次引导()
  }
  延迟显示底栏()

  // 图片翻译ref.value.打开model()
})
</script>

<style lang="scss" scoped>
@property --tool-bar-color-a {
  syntax: '<color>';
  inherits: false;
  initial-value: #fff0;
}

@property --tool-bar-color-b {
  syntax: '<color>';
  inherits: false;
  initial-value: #fff0;
}

.icon {
  @apply text-22px text-#999 cursor-pointer transition-cusbezier-400 [&:active]:(I_text-[var(--primary-color)] dark:I_text-#fff) hover:(text-[#666] dark:text-#d9d9d9);
}

// 文本域公用样式
.text_wrapper {
  :deep(.arco-textarea) {
    font-size: v-bind(文字尺寸);

    @apply h-full pr-26px  pb-50px resize-none ;
  }

  :deep(.arco-textarea-word-limit) {
    right: v-bind(text_limit_right) !important;

    @apply text-#ccc transition-cusbezier-250 absolute z-1 bottom-9px dark:text-#fff/35;
  }

  :deep(.arco-textarea-wrapper) {
    background-color: transparent;
    border-color: var(--main-textarea-border-color);
  }

  :deep(.arco-textarea-focus) {
    border-color: var(--main-textarea-border-focus-color);
  }
}

// 下面的文本域样式
.text_readonly {
  position: relative;

  :deep(.arco-textarea-focus) {
    // 它focus和正常状态一样的颜色, 来营造即使focus了, 也是没有颜色变化的
    border-color: var(--main-textarea-border-color);
  }

  :deep(.arco-textarea-wrapper) {
    // 暗色下的结果框, 不要边框
    @apply dark:border-none;
  }
}

.tools_wrapper {
  :deep(.arco-select-view-single) {
    padding-left: 16px;
    padding-right: 16px;
  }

  :deep(.arco-radio-checked) {
    @apply dark:(text-#fff I_bg-#222 I_text-shadow-[0_3px_15px_#ffffffb8]);
  }
}

.tools_bar_wrapper {
  &::before {
    content: '';
    position: absolute;
    border-bottom: none;
    top: -100%;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 200px);
    height: 100%;
    backdrop-filter: blur(2px);
    transition:
      --tool-bar-color-a 0.5s var(--ani-bezier),
      --tool-bar-color-b 0.5s var(--ani-bezier),
      width 0.3s var(--ani-bezier);
    background-image: linear-gradient(
      to top,
      var(--tool-bar-color-a) 0%,
      var(--tool-bar-color-b) 100%
    );

    // background-image: linear-gradient(to top, #000 0%, #000 100%);
    border-radius: 8px 8px 0 0;
  }

  &:hover {
    &::before {
      width: 100%;

      --tool-bar-color-a: var(--tools-bar-bottom-color);
      --tool-bar-color-b: rgb(var(--primary-rgb-color-without-comma) / 6%);
    }
  }
}

.main {
  background-image: linear-gradient(to bottom, var(--main-bg-color-01) 0%, var(--main-bg-color-02) 50%);
}
</style>
