import { IconCheckCircleFill, IconStop } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import { isArray, isPlainObject, sortBy, transform } from 'lodash-es'

/**
 * 替换字符串中的占位符
 * @param str 要替换的字符串
 * @param replace [要替换的字符串, 替换后的字符串]
 * @returns 替换后的字符串
 * @example replaceStr('测试文字, 测试段落', ['测试', 'foo']) // 'foo文字, foo段落'
 */
export function replaceStr(str: string, replace: [string, string]) {
  return str.replaceAll(replace[0], replace[1])
}

/**
 * hex转Rgb(a)
 * @param hex 要转换的hex值,如#333、#3338、#a1b2c3、#a1b2c388
 * @returns rgb(a)值, 如 '91, 97, 255'或'91, 97, 255, 0.53'
 * @example hexToRgb('#333') // '51, 51, 51'
 */
export function hexToRgb(hex: string) {
  const throwError = () => {
    throw new Error('传入了错误的Hex值')
  }
  const isHexColor = (value: string) => {
    return /^#([A-F0-9]{3}|[A-F0-9]{4}|[A-F0-9]{6}|[A-F0-9]{8})$/i.test(value)
  }
  if (!isHexColor(hex)) {
    throwError()
  }
  const cleanHex = hex.replace('#', '')
  const length = cleanHex.length
  const parseHex = (offset: number) => {
    return Number.parseInt(cleanHex.substring(offset, offset + 2), 16)
  }

  const getRgb = () => {
    switch (length) {
      case 3:
        return [parseHex(0), parseHex(1), parseHex(2)]
      case 4:
        return [parseHex(0), parseHex(1), parseHex(2), parseHex(3) / 255]
      case 6:
        return [parseHex(0), parseHex(2), parseHex(4)]
      case 8:
        return [parseHex(0), parseHex(2), parseHex(4), parseHex(6) / 255]
      default:
        return throwError()
    }
  }
  const [r, g, b, a = 1] = getRgb()
  return `${r}, ${g}, ${b}${a !== 1 ? `, ${a}` : ''}`
}

/**
 * 打开链接
 * @param url 要打开的链接
 * @example openUrl('https://www.baidu.com')
 * @description utools环境下使用utools的shellOpenExternal方法, 否则使用window.open
 */
export function openUrl(url: string) {
  window?.utools ? window.utools.shellOpenExternal(url) : globalThis.open(url)
}

/*
 * @param type 提示类型, success或error
 * @param content 提示内容
 * @param duration 提示持续时间
 * @example 针对一些开关用的提示, 修改了默认的图标, switchMessage('success', '开启成功'), switchMessage('error', '已关闭')
 */
export function switchMessage(
  type: 'success' | 'error',
  content: string,
  duration = 2000,
) {
  Message[type]({
    content,
    duration,
    icon: () => h(type === 'success' ? IconCheckCircleFill : IconStop),
  })
}

const emoticonArr = [
  '(´⌒`｡)',
  '(๑•̀ㅂ•́)و✧',
  '(ー_ー)!!',
  '╮(╯▽╰)╭',
  '(｡í _ ì｡)',
  ' (╯°Д°)╯︵ ┻━┻',
  '(╯ರ ~ ರ) ╯︵ ┻━┻',
  '(ノ°ο°)ノ',
  '(º Д º*)',
  '(；￣Д￣) ',
  '(」ﾟヘﾟ)」',
  '(っ °Д °;)っ',
  'Ծ‸ Ծ ',
  '¯_(ツ)_/¯',
  '(ಠ_ಠ)',
]

/**
 *
 * @param minOrMax 最小值或最大值
 * @param max  最大值
 * @returns  返回一个介于min和max之间(包含)的随机整数
 * @example getRandomIntInclusive(3, 10) // 3-10之间的随机整数
 * @example getRandomIntInclusive(8) // 0-8之间的随机整数
 */
export function getRandomIntInclusive(minOrMax: number, max?: number) {
  const min = max === undefined ? 0 : Math.ceil(minOrMax)
  max = max === undefined ? Math.floor(minOrMax) : Math.floor(max)
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 随机获取一个颜文字表情
export function getRandomEmoticon() {
  return emoticonArr[getRandomIntInclusive(emoticonArr.length - 1)]
}

export function fileToBlob(file: File): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onerror = () => reject(reader.error || new Error('读取文件失败'))
    reader.onload = () =>
      resolve(new Blob([reader.result as ArrayBuffer], { type: file.type }))
    reader.readAsArrayBuffer(file)
  })
}

export function blobToUrl(blob: Blob) {
  return URL.createObjectURL(blob)
}

/**
 * 将对象中的所有一维数组进行排序
 * @param obj 一个对象
 * @returns  返回一个新对象，其中所有数组都已排序
 */
export function sortArraysInObject<T extends Record<string, any>>(obj: T): T {
  return transform(obj, (result: any, value: any, key: keyof any) => {
    if (isArray(value)) {
      // 检查数组是否仅包含基本数据类型 (数字, 字符串)
      if (value.every(item => typeof item === 'number' || typeof item === 'string')) {
        result[key] = sortBy([...value])
      }
      else {
        // 如果数组含有非基本数据类型，则不进行排序，直接赋值
        result[key] = [...value]
      }
    }
    else if (isPlainObject(value)) {
      // 递归处理纯 JavaScript 对象，避免其他如 DOM 元素等类型
      result[key] = sortArraysInObject(value)
    }
    else {
      // 其它类型的值直接复制
      result[key] = value
    }
  }) as T
}
