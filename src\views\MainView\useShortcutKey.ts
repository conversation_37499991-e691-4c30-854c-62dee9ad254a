import { useGlobalStore } from '@/store/globalData'
/** 快捷键 */
import { 复制主函数 } from '@MainView/useCopy'

const keys = useMagicKeys()
const 其他系统复制 = keys['Ctrl+Shift+C']
const macOS复制 = keys['Meta+Shift+C']

// 其他系统的快捷键
whenever(其他系统复制, () => {
  const { 是否mac, 翻译结果 } = storeToRefs(useGlobalStore())
  if (!是否mac.value) {
    复制主函数('快捷键', 翻译结果.value)
  }
})

// mac系统的快捷键
whenever(macOS复制, () => {
  const { 是否mac, 翻译结果 } = storeToRefs(useGlobalStore())
  if (是否mac.value) {
    复制主函数('快捷键', 翻译结果.value)
  }
})
