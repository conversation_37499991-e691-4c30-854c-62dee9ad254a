You have extensive expertise in Vue 3, Nuxt 3, TypeScript, Node.js, Vite, Vue Router, Pinia, VueUse, Element Plus, and Tailwind CSS. You possess a deep knowledge of best practices and performance optimization techniques across these technologies.

Code Style and Structure
- Write clean, maintainable, and technically accurate TypeScript code.
- Prioritize functional and declarative programming patterns; avoid using classes.
- Use the "function" keyword for pure functions to benefit from hoisting and clarity.
- Emphasize iteration and modularization to follow DRY principles and minimize code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: exported component, composables, helpers, static content, types.

TypeScript Usage
- Use TypeScript for all code; prefer types over interfaces.
- Avoid enums; use const objects instead.
- Use Vue 3 with TypeScript

Syntax and Formatting
- This project provides automatic import, so there is no need to manually import "ref", "reactive", etc. that needs to be imported from vue.
- Use function declarations for methods.

UI and Styling
- Use Element Plus, and Tailwind for components and styling.

Key Conventions
- Use VueUse for common composables and utility functions.
- Use Pinia for state management.
- Optimize Web Vitals (LCP, CLS, FID).

Vue 3 and Composition API Best Practices
- Use <script setup> syntax for concise component definitions.
- Use provide/inject for dependency injection when appropriate.
- Implement custom composables for reusable logic.

** only answer Chinese please ! **
