<template>
  <div>
    <CusFullModal
      :visible="modal可见"
      modal-animation-name="zoom-rb"
      @open="打开model()"
      @cancel="modal取消()"
      @close="modal关闭动画结束()"
    >
      <template #header_left>
        <span class="font-500">设置</span>
      </template>

      <template #header_right>
        <ModalTitleCloseBtn @click="modal取消()" />
      </template>

      <template #default="{ modalSizeInfo }">
        <section p="x-20px y-16px" class="setting_wrapper w-full overflow-x-hidden">
          <div class="w-full flex">
            <div class="w-70%">
              <SettingCard @mouseenter="切换文案()">
                <template #title>
                  <div class="horizontal-place-4px">
                    <i i-fluent-emoji-flat-wind-chime class="text-19px" />
                    <span>一些提示</span>
                  </div>
                </template>
                <FormItemWrapper>
                  <ul class="mb-18px list-disc pl-16px">
                    <li>
                      谷歌翻译可以直接使用, 且不需要任何特殊网络环境；其他的你需要自行申请之后才能使用它们, 他们都是免费的！
                      <a-link
                        id="guide-link"
                        class="-indent-4px"
                        @click="openUrl(utoolsConfig.docUrl)"
                      >
                        点击这里, 了解如何申请~
                      </a-link>
                      <!-- <i class="text-18px" i-twemoji-grinning-face></i> -->
                    </li>
                    <li @mouseenter="切换文案('关于自动复制')">
                      如果你使用uTools的全局快捷键功能, 请将关键字请设置为 “<span
                        class="code_font select-all text-15px text_important tracking-wider"
                      >fjyi</span>”
                    </li>
                    <li>
                      遇到问题？或者有好的建议？请通过uTools评论区告诉我们
                    </li>
                  </ul>
                </FormItemWrapper>
              </SettingCard>
              <a-form ref="settingFormRef" auto-label-width :model="formData">
                <a-row>
                  <section class="mt-18px flex-1 space-y-18px">
                    <SettingCard>
                      <template #title>
                        <div class="horizontal-place-6px">
                          <i i-fluent-emoji-flat-gear class="text-17px" />
                          <span>基本设置</span>
                        </div>
                      </template>
                      <FormItemWrapper>
                        <a-form-item label="翻译服务" @mouseenter="切换文案('翻译服务')">
                          <a-checkbox-group
                            v-model="formData.homeHasApi"
                            :max="可选择的服务数量"
                          >
                            <a-checkbox
                              v-for="item in api列表"
                              :key="item.value"
                              :value="item.value"
                            >
                              <span class="inline-block min-w-60px">
                                {{ item.label }}
                              </span>
                            </a-checkbox>
                          </a-checkbox-group>
                        </a-form-item>
                        <a-form-item
                          label="主页显示顺序"
                          @mouseenter="切换文案('主页显示顺序')"
                        >
                          <div class="flex flex-wrap gap-4px">
                            <div
                              v-for="(item, index) in 显示顺序data"
                              :key="item.value"
                              class="flex items-center"
                            >
                              <i
                                v-if="index > 0"
                                i-ic-twotone-keyboard-double-arrow-right
                                class="mr-4px mt-1px text-#777:70"
                              />
                              <div class="icon_wrapper">
                                <ApiIcon :data="item.value" />
                                <div class="icon_text">
                                  {{ item.label }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </a-form-item>
                        <a-form-item
                          label="文本框字号"
                          @mouseenter="切换文案('文本框字号')"
                        >
                          <a-input-number
                            v-model="formData.textFont"
                            :min="14"
                            :max="20"
                            placeholder="请输入文本框字号 (14 ~ 20) "
                            mode="button"
                          >
                            <template #suffix>
                              像素
                            </template>
                          </a-input-number>
                        </a-form-item>
                        <a-form-item
                          label="翻译触发方式"
                          @mouseenter="切换文案('翻译触发方式')"
                        >
                          <a-radio-group v-model="formData.translationTriggerMode">
                            <a-radio
                              v-for="item in 翻译触发方式选项"
                              :key="item.value"
                              :value="item.value"
                            >
                              <div v-if="item.value === 'input'">
                                {{ item.label }}
                              </div>
                              <div v-else class="space-x-6px">
                                <span>点击</span>
                                <MimicryBtn
                                  disabled
                                  class="pointer-events-none inline-flex translate-y-1px !min-h-22px !min-w-22px !cursor-pointer"
                                >
                                  <i class="i-cus-yi" />
                                </MimicryBtn>
                                <span class="inline-block">或按下回车时</span>
                              </div>
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                        <a-form-item
                          label="快捷键行为"
                          @mouseenter="切换文案('快捷键行为')"
                        >
                          <a-radio-group v-model="formData.copyBtnBehavior">
                            <a-radio
                              v-for="item in 快捷键行为选项"
                              :key="item.value"
                              :value="item.value"
                            >
                              {{ item.label }}
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                        <a-form-item
                          label="底部显示的按钮"
                          @mouseenter="切换文案('底部显示的按钮')"
                        >
                          <a-checkbox-group v-model="formData.copyBtnShow">
                            <a-checkbox
                              v-for="item in 显示按钮选项"
                              :key="item.value"
                              :value="item.value"
                            >
                              {{ item.label }}
                            </a-checkbox>
                          </a-checkbox-group>
                        </a-form-item>
                        <a-form-item
                          label="首选译文语言"
                          class="flex items-start"
                          @mouseenter="切换文案('首选译文语言')"
                        >
                          <a-radio-group v-model="formData.defaultForeignLanguage">
                            <a-radio
                              v-for="item in 首选译文语言选项"
                              :key="item.value"
                              :value="item.value"
                            >
                              {{ item.label }}
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                        <a-form-item
                          label="去除译文换行"
                          class="flex items-start"
                          @mouseenter="切换文案('去除译文换行')"
                        >
                          <a-switch v-model="formData.removeN" class="ml-5px" />
                        </a-form-item>
                        <a-form-item
                          label="三击退格清空输入"
                          class="flex items-start"
                          @mouseenter="切换文案('三击退格清空输入')"
                        >
                          <a-switch
                            v-model="formData.tripleBackspaceClear"
                            class="ml-5px"
                          />
                        </a-form-item>
                        <a-form-item label="插件主题" @mouseenter="切换文案('插件主题')">
                          <a-radio-group v-model="formData.theme">
                            <a-radio
                              v-for="item in 插件主题选项"
                              :key="item.value"
                              :value="item.value"
                            >
                              {{ item.label }}
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                        <a-form-item
                          label="命名模式设置"
                          @mouseenter="切换文案('命名模式设置')"
                        >
                          <a-button size="small" @click="打开命名设置弹窗()">
                            命名模式设置
                          </a-button>
                        </a-form-item>
                        <a-form-item
                          label="最近记录数"
                          @mouseenter="切换文案('最近记录数')"
                        >
                          <a-input-number
                            v-model="formData.lastDirectionStorageNum"
                            :min="2"
                            :max="10"
                            placeholder="请输入最近翻译方向记录数量 (2 ~ 10) "
                            mode="button"
                          >
                            <template #suffix>
                              条记录
                            </template>
                          </a-input-number>
                          <a-button type="primary" class="ml-20px" @click="清除最近翻译方向记录">
                            清除记录
                          </a-button>
                        </a-form-item>
                      </FormItemWrapper>
                    </SettingCard>

                    <SettingCard>
                      <template #title>
                        <div class="horizontal-place-6px">
                          <i i-twemoji-speaker-high-volume class="text-16px" />
                          <span>朗读设置</span>
                        </div>
                      </template>
                      <FormItemWrapper>
                        <a-form-item label="语音朗读" @mouseenter="切换文案('语音朗读')">
                          <a-switch v-model="formData.readAloud" class="ml-5px" />
                        </a-form-item>
                        <a-form-item label="原文朗读" @mouseenter="切换文案('原文朗读')">
                          <div>
                            <span>填写</span>
                            <div
                              bg="#f2f3f5 dark:#3b3b3c"
                              class="icon_wrapper icon_show mx-4px !inline-flex"
                              :style="{ verticalAlign: '-5px' }"
                            >
                              <ApiIcon data="transmart" />
                              <div class="icon_text !max-w-60px">
                                腾讯交互
                              </div>
                            </div>
                            的信息, 即可实现更精准的朗读角色匹配,
                            <a-link
                              @click="
                                openUrl(
                                  'https://flowus.cn/share/14717519-036e-4d92-9ba6-06d8ee052e7b',
                                )
                              "
                            >
                              点击去了解如何申请
                            </a-link>
                          </div>
                        </a-form-item>
                        <a-form-item label="朗读模式" @mouseenter="切换文案('朗读模式')">
                          <a-radio-group
                            v-model="formData.readingModel"
                            :disabled="!formData.readAloud"
                          >
                            <a-radio
                              v-for="item in 朗读模式选项"
                              :key="item.value"
                              :value="item.value"
                            >
                              {{ item.label }}
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                        <a-form-item label="朗读偏好" @mouseenter="切换文案('朗读偏好')">
                          <a-radio-group
                            v-model="formData.readingPreference"
                            :disabled="
                              !formData.readAloud || formData.readingModel === '离线'
                            "
                          >
                            <a-radio
                              v-for="item in 朗读偏好选项"
                              :key="item.value"
                              :value="item.value"
                            >
                              {{ item.label }}
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                      </FormItemWrapper>
                    </SettingCard>
                    <SettingCard
                      class="cus-divide"
                      @mouseenter="切换文案('翻译服务数据')"
                    >
                      <template #title>
                        <div class="horizontal-place-4px">
                          <i i-flat-color-icons-menu class="text-18px" />
                          <span>翻译服务数据</span>
                        </div>
                      </template>

                      <FormItemWrapper
                        id="ali"
                        title="阿里翻译"
                        icon-data="ali"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="AccessKey ID">
                          <a-input
                            v-model.trim="formData.accessKeyId"
                            placeholder="请输入阿里云AccessKey ID"
                            allow-clear
                          />
                        </a-form-item>
                        <a-form-item label="AccessKey Secret">
                          <a-input-password
                            v-model.trim="formData.accessKeySecret"
                            placeholder="请输入阿里云AccessKey Secret"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>

                      <FormItemWrapper
                        id="baidu"
                        title="百度翻译"
                        icon-data="baidu"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="APP ID">
                          <a-input
                            v-model.trim="formData.appid"
                            placeholder="请输入百度翻译的APP ID"
                            allow-clear
                          />
                        </a-form-item>
                        <a-form-item label="密钥">
                          <a-input-password
                            v-model.trim="formData.token"
                            placeholder="请输入百度翻译的密钥"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>

                      <FormItemWrapper
                        id="huoshan"
                        title="火山翻译"
                        icon-data="huoshan"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="AccessKeyID">
                          <a-input
                            v-model.trim="formData.huoshanAccessKeyId"
                            placeholder="请输入火山翻译AccessKeyID"
                            allow-clear
                          />
                        </a-form-item>
                        <a-form-item label="SecretAccessKey">
                          <a-input-password
                            v-model.trim="formData.huoshanSecretAccessKey"
                            placeholder="请输入火山翻译SecretAccessKey"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>

                      <FormItemWrapper
                        id="tencent"
                        title="腾讯云翻译"
                        icon-data="tencent"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="Secret Id">
                          <a-input
                            v-model.trim="formData.secretId"
                            placeholder="请输入腾讯翻译Secret Id"
                            allow-clear
                          />
                        </a-form-item>
                        <a-form-item label="Secret Key">
                          <a-input-password
                            v-model.trim="formData.secretKey"
                            placeholder="请输入腾讯翻译Secret Key"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>

                      <FormItemWrapper
                        id="transmart"
                        title="腾讯交互"
                        icon-data="transmart"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="用户名">
                          <a-input
                            v-model.trim="formData.transmartName"
                            label="Token"
                            placeholder="请输入腾讯交互-账号信息-用户名"
                            allow-clear
                          />
                        </a-form-item>
                        <a-form-item label="Token">
                          <a-input-password
                            v-model.trim="formData.transmartToken"
                            placeholder="请输入腾讯交互的Token"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>

                      <FormItemWrapper
                        id="xiaoniu"
                        title="小牛翻译"
                        icon-data="xiaoniu"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="API-KEY">
                          <a-input-password
                            v-model.trim="formData.xiaoniuApiKey"
                            placeholder="请输入小牛翻译API-KEY"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>

                      <FormItemWrapper
                        id="youdao"
                        title="有道翻译"
                        icon-data="youdao"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="应用ID">
                          <a-input
                            v-model.trim="formData.youdaoId"
                            placeholder="请输入有道智云应用ID"
                            allow-clear
                          />
                        </a-form-item>
                        <a-form-item label="应用密钥">
                          <a-input-password
                            v-model.trim="formData.youdaoSecret"
                            placeholder="请输入有道智云应用密钥"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>
                      <FormItemWrapper
                        id="deepl"
                        title="DeepL"
                        icon-data="deepl"
                        is-server
                      >
                        <a-form-item label="接口类型">
                          <a-radio-group v-model="formData.deeplType">
                            <a-radio v-for="(item, index) in DeepL接口选项" :key="index" :value="item.value">
                              {{ item.label }}
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                        <TransitionGroup name="custom-fade">
                          <template v-if="formData.deeplType === 'deeplx'">
                            <a-form-item label="相关资料">
                              <div class="space-x-16px">
                                <a-link @click="openUrl('https://flowus.cn/share/49e24dbe-c073-43b4-8d37-29fc4a0dd6ba/')">
                                  自己部署的方式
                                </a-link>
                                <CusTooltip content="" position="right">
                                  <template #content>
                                    <div class="select-none">
                                      <p>复制出来的链接</p>
                                      <p>需要以 <span class="select-all">/translate</span> 结尾</p>
                                      <p>填入下方API地址即可使用</p>
                                    </div>
                                  </template>
                                  <a-link @click="openUrl('https://flowus.cn/share/74c491e2-6e87-4205-92be-1feada33b0ce/')">
                                    可免费用的API
                                  </a-link>
                                </CusTooltip>
                              </div>
                            </a-form-item>
                            <a-form-item label="API地址" field="deeplxUrlField">
                              <div ref="deeplxUrlDomRef" class="w-full">
                                <a-input
                                  v-model.trim="formData.deeplxUrl"
                                  placeholder="DeepLX的API地址, 如: https://xxx.com/translate"
                                  allow-clear
                                  @focus="settingFormRef?.setFields({
                                    deeplxUrlField: { status: 'validating' },
                                  })"
                                  @blur="deeplx链接输入框失焦()"
                                />
                              </div>
                            </a-form-item>
                            <a-form-item label="Token" field="deeplxTokenField">
                              <a-input-password
                                v-model.trim="formData.deeplxToken"
                                placeholder="DeepLX的API的Token(如未设置, 请留空)"
                                allow-clear
                                @focus="settingFormRef?.setFields({
                                  deeplxTokenField: { status: 'validating' },
                                })"
                                @blur="deeplxToken输入框失焦()"
                              />
                            </a-form-item>
                          </template>
                          <template v-else>
                            <a-form-item label="密钥">
                              <a-input-password
                                v-model.trim="formData.deeplKey"
                                placeholder="请输入DeepL密钥"
                                allow-clear
                              />
                            </a-form-item>
                          </template>
                        </TransitionGroup>
                      </FormItemWrapper>
                      <FormItemWrapper
                        v-if="false"
                        id="deepl"
                        title="谷歌翻译"
                        icon-data="google"
                        is-server
                      >
                        <a-form-item label="接口类型">
                          <a-radio-group v-model="formData.googleType">
                            <a-radio v-for="(item, index) in google接口选项" :key="index" :value="item.value">
                              {{ item.label }}
                            </a-radio>
                          </a-radio-group>
                        </a-form-item>
                      </FormItemWrapper>

                      <FormItemWrapper
                        id="caiyun"
                        title="彩云小译"
                        icon-data="caiyun"
                        is-server
                        @mouseenter="切换文案('翻译服务数据')"
                      >
                        <a-form-item label="令牌">
                          <a-input-password
                            v-model.trim="formData.caiyunToken"
                            placeholder="请输入彩云小译令牌"
                            allow-clear
                          />
                        </a-form-item>
                      </FormItemWrapper>
                    </SettingCard>
                  </section>
                </a-row>
              </a-form>
              <SettingCard class="mt-18px" @mouseenter="切换文案('配置信息')">
                <template #title>
                  <div class="horizontal-place-6px">
                    <i i-twemoji-optical-disk class="text-16px" />
                    <span>配置信息</span>
                  </div>
                </template>
                <div class="mb-20px space-x-16px">
                  <a-button
                    :type="当前主题 === 'light' ? 'outline' : 'primary'"
                    @click="打开导入弹窗()"
                  >
                    <template #icon>
                      <i class="mb-1px text-20px" i-line-md-download-loop />
                    </template>
                    导入
                  </a-button>

                  <a-button
                    :type="当前主题 === 'light' ? 'outline' : 'primary'"
                    @click="打开导出弹窗()"
                  >
                    <template #icon>
                      <i class="mb-1px text-20px" i-line-md-upload-loop />
                    </template>
                    导出
                  </a-button>
                </div>
              </SettingCard>
            </div>
            <div class="right_main relative ml-16px transition-all">
              <SettingCard
                class="fixed w-[calc(30%-24px)] pb-16px"
                :style="{
                  insetBlock: `${modalSizeInfo.headerHeight}px ${modalSizeInfo.footerHeight}px`,
                }"
              >
                <template #title>
                  <div class="horizontal-place-6px">
                    <i i-noto-v1-bookmark class="mb-2px text-18px" />
                    <span>选项说明</span>
                  </div>
                </template>
                <div
                  flex="~ col"
                  class="text-[var(--color-text-2)] leading-relaxed"
                  v-html="
                    解释文案
                      || '鼠标悬浮左侧选项上可查看对应选项说明, 在遇到问题时请先看这里有没有相关解释！'
                  "
                />
              </SettingCard>
            </div>
          </div>
        </section>
      </template>

      <template #footer_left>
        <a-popconfirm
          position="tl"
          content-class="popconfirm_wrapper"
          type="warning"
          content="确定要重置本插件的数据吗？重置可以解决大部分问题, 但在此之前请备份好相关服务的信息哦~"
          @ok="重置数据()"
        >
          <a-button type="outline" status="danger">
            重置插件数据
          </a-button>
        </a-popconfirm>
      </template>
      <template #footer_right>
        <a-button @click="modal取消()">
          取消
        </a-button>
        <a-button type="primary" @click="设置modal确定()">
          确定
        </a-button>
      </template>
      <!-- </div> -->
    </CusFullModal>

    <!-- 导入弹窗 -->
    <ImportModal ref="导入弹框Ref" @import-submit="导入提交($event)" />

    <!-- 导出弹窗 -->
    <ExportModal ref="导出弹框Ref" @export-submit="导出提交($event)" />
  </div>
</template>

<script setup lang="ts">
import type { 引导options类型 } from '@/components/SettingModal/SettingsTypes'
import {
  api选项,
  DeepL接口选项,
  google接口选项,
  快捷键行为选项,
  插件主题选项,
  文案映射,
  显示按钮选项,
  朗读偏好选项,
  朗读模式选项,
  翻译触发方式选项,
  首选译文语言选项,
} from '@/components/SettingModal/SettingsData'
import {
  useGlobalStore,
  快捷键文案,
  获取存储项,
  设置存储,
} from '@/components/SettingModal/SettingsModules'
import { 显示引导, 清除引导 } from '@/components/SettingModal/SettingsUtils'
import { utoolsConfig } from '@/config/utoolsConfig'
import { 清除历史记录 } from '@/utils/lastLan'
import { Message } from '@arco-design/web-vue'
import { cloneDeep, compact, isEqual } from 'lodash-es'
import isURL from 'validator/es/lib/isURL'

defineOptions({
  name: 'SettingModal',
})
const emits = defineEmits<{
  ok: [{ 是否改动: boolean }]
  cancel: []
  reset: []
  openNameSetting: []
  更新语法树
}>()
const { 当前主题 } = storeToRefs(useGlobalStore())

const api列表 = ref(api选项)
const modal可见 = ref(false)
const 导入弹框Ref = ref()
const 导出弹框Ref = ref()
const settingFormRef = ref()
const 解释文案 = ref('') // 解释文案
const formData = reactive({
  homeHasApi: ['baidu', 'tencent', 'youdao', 'ali'], // 首页展示的翻译方式
  textFont: 16, // 文本框字号
  copyBtnBehavior: 'open', // 快捷键的行为
  copyBtnShow: [1, 2, 3], // 首页显示的按钮
  readAloud: true, // 语音朗读
  readingModel: '在线', // 语音朗读模式
  readingPreference: 'default', // 朗读偏好
  codeMode: false, // 命名翻译模式
  defaultForeignLanguage: 'en', // 首选译文语言
  defaultApi: '', // 默认翻译方式
  removeN: false, // 去除译文换行
  tripleBackspaceClear: false, // 三次退格清空
  theme: 'auto', // 主题
  appid: undefined, // 百度
  token: undefined, // 百度
  secretId: undefined, // 腾讯
  secretKey: undefined, // 腾讯
  accessKeyId: undefined, // 阿里
  accessKeySecret: undefined, // 阿里
  youdaoId: undefined, // 有道
  youdaoSecret: undefined, // 有道
  caiyunToken: undefined, // 彩云
  huoshanAccessKeyId: undefined, // 火山
  huoshanSecretAccessKey: undefined, // 火山
  xiaoniuApiKey: undefined, // 小牛
  transmartName: undefined, // 腾讯交互新字段
  transmartToken: undefined, // 腾讯交互
  deeplKey: undefined, // DeepL密钥
  deeplxUrl: undefined, // DeepLX请求地址
  deeplxToken: undefined, // DeepLX密钥
  deeplType: 'Free', // DeepL接口类型  Free|Pro|deeplx
  translationTriggerMode: 'input', // 翻译触发方式
  googleType: 'online', // 谷歌翻译接口类型  local|online|custom
  lastDirectionStorageNum: undefined,
})

const 显示顺序data: any = computed(() => {
  const 所有服务 = cloneDeep(api列表.value)
  const 选的服务 = cloneDeep(formData.homeHasApi)
  const arr = 选的服务.map((i, idx) => {
    return {
      ...所有服务.find(j => j.value === i),
      sort: idx,
    }
  })
  return arr
})

const { 获取设置, 保存设置, 重置设置, 导入配置, 导出设置, getSettingToFromData } = 设置存储(formData)
const 首页的api数组 = ref<string[]>([]) // 当前首页展示的翻译方式

const 已勾选的翻译 = computed(() => {
  return api列表.value.filter(i => 首页的api数组.value.includes(i.value))
})

const { copy: 复制 } = useClipboard() // 复制结果功能

// 监听首页翻译方式的checkbox勾选数量
const 可选择的服务数量 = ref(7)
watchEffect(() => {
  const 已选择的api长度 = formData.homeHasApi?.length
  if (已选择的api长度 > 可选择的服务数量.value) {
    formData.homeHasApi = 首页的api数组.value
    Message.warning({
      content: `最多只能选择${可选择的服务数量.value}个翻译方式哦~`,
      duration: 2500,
    })
  }
  else if (已选择的api长度 < 1) {
    formData.homeHasApi = 首页的api数组.value
    Message.warning({
      content: '还是至少留下1个翻译方式吧！',
      duration: 2500,
    })
  }
  首页的api数组.value = formData.homeHasApi
})

// 监听默认翻译方式的下拉选项
// 如果选择了"默认翻译方式"为"首页翻译方式"不存在的, 则把可用的翻译方式第一个赋值给默认
watchEffect(() => {
  if (!首页的api数组.value.includes(formData.defaultApi)) {
    formData.defaultApi = 已勾选的翻译.value[0].value
  }
})

const deeplxUrlDomRef = ref() // deeplx API地址输入框的外层dom

function 判断链接合法(url: string) {
  const url判断规则 = {
    protocols: ['http', 'https'],
    require_protocol: true,
  }
  return isURL(url, url判断规则)
}

function 是字母和数字加等号(str: string) {
  const regex = /^[A-Z0-9=]+$/i
  return regex.test(str)
}

function deeplx地址字段标红(messageContent?: string) {
  settingFormRef.value.setFields({
    deeplxUrlField: {
      status: 'error',
      message: messageContent || '',
    },
  })
}

function deeplxToken字段标红() {
  settingFormRef.value.setFields({
    deeplxTokenField: {
      status: 'error',
      message: '只能包含大小写英文、数字，符号只允许等号',
    },
  })
}

function deeplx链接输入框失焦() {
  if (formData.deeplxUrl && !判断链接合法(formData.deeplxUrl)) {
    deeplx地址字段标红('这可能不是一个合理的链接')
  }
}

function deeplxToken输入框失焦() {
  if (formData.deeplxToken && !是字母和数字加等号(formData.deeplxToken)) {
    deeplxToken字段标红()
  }
}
enum DeepLXStatus {
  不用校验 = 0,
  校验通过 = 1,
  校验失败 = 2,
  未填写 = 3,
}

interface 校验配置 {
  校验函数: (value: string) => boolean
  校验通过信息?: string
  校验未通过信息: string
  未填写信息: string
}

interface 校验结果Type {
  状态码: DeepLXStatus
  提示信息: string
}

function 生成校验结果({
  校验函数,
  校验通过信息 = '',
  校验未通过信息,
  未填写信息,
}: 校验配置): ((deeplType: string, value: string) => 校验结果Type) {
  return (deeplType, value): 校验结果Type => {
    if (deeplType !== 'deeplx') {
      return { 状态码: DeepLXStatus.不用校验, 提示信息: 校验通过信息 }
    }
    if (!value) {
      return { 状态码: DeepLXStatus.未填写, 提示信息: 未填写信息 }
    }
    return 校验函数(value)
      ? { 状态码: DeepLXStatus.校验通过, 提示信息: 校验通过信息 }
      : { 状态码: DeepLXStatus.校验失败, 提示信息: 校验未通过信息 }
  }
}

function 执行DeepLX校验() {
  const deepl链接配置: 校验配置 = {
    校验函数: 判断链接合法,
    校验未通过信息: 'DeepLX的「API地址」不是合理的链接',
    未填写信息: '设置成功, 但DeepLX的「API地址」未填写, DeepL可能无法正常使用',
  }
  const deeplToken配置: 校验配置 = {
    校验函数: 是字母和数字加等号,
    校验未通过信息: 'DeepLX的「Token」只能包含大小写英文、数字，符号只允许等号',
    未填写信息: '',
  }
  const 生成deeplx链接校验结果 = 生成校验结果(deepl链接配置)
  const 生成deeplxToken校验结果 = 生成校验结果(deeplToken配置)
  const url校验结果 = 生成deeplx链接校验结果(formData.deeplType, formData.deeplxUrl || '')
  const token校验结果 = 生成deeplxToken校验结果(formData.deeplType, formData.deeplxToken || '')
  const 校验结果数组 = [url校验结果, token校验结果]

  const 错误信息 = []
  const 警告信息 = []

  for (const { 状态码, 提示信息 } of 校验结果数组) {
    if (状态码 === DeepLXStatus.校验失败) {
      错误信息.push(提示信息)
    }
    else if (状态码 === DeepLXStatus.未填写) {
      警告信息.push(提示信息)
    }
  }

  // 如果存在错误信息，展示错误信息并终止流程
  if (compact(错误信息).length > 0) {
    Message.error({ content: 错误信息.join('; '), duration: 3000 })
    return 500
  }
  else if (compact(警告信息).length > 0) {
    Message.warning({ content: 警告信息.join('; '), duration: 3000 })
    return 300
  }
  else {
    return 200
  }
}
const 设置备份 = ref()
function 打开弹窗(target = '') {
  modal可见.value = true
  获取设置()
  设置备份.value = sortArraysInObject(getSettingToFromData())
  setTimeout(() => {
    if (target) {
      const el = document.querySelector(`#${target}`)
      el?.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, 600)
}

function 设置modal确定() {
  const deeplx表单校验结果 = 执行DeepLX校验()
  if (deeplx表单校验结果 === 500) { // DeepLX表单校验失败, 不保存设置
    return
  }
  else if (deeplx表单校验结果 === 200) { // DeepLX表单校验通过, 继续执行后续操作
    Message.success({ content: '设置成功', duration: 1000 })
  }
  保存设置()
  const 保存后的用户配置 = sortArraysInObject(getSettingToFromData())
  emits('ok', { 是否改动: !isEqual(保存后的用户配置, 设置备份.value) })
  关闭弹窗()
}

// 点击弹框取消
function modal取消() {
  清除引导()
  emits('cancel')
  关闭弹窗()
}

// 打开设置弹框 (动画结束) 回调
function 打开model() {
  if (!获取存储项('firstUseSetting')) {
    首次引导()
  }
}

// 首次提示链接位置
function 首次引导() {
  const option: 引导options类型 = {
    id: 'firstUseSetting',
    title: '这里有一些提示',
    text: '这可是我起早贪黑写的, 你可以在点击”关闭“按钮后点击链接查看, 它可以帮助你申请到这些免费的服务, 如果你已经是个老手了, 那就关闭这个对话框开始使用吧~',
    attachTo: {
      element: '#guide-link',
      on: 'bottom',
    },
    classes: 'guide_wrapper',
  }
  显示引导(option, 'firstUseSetting')
}

function 关闭弹窗() {
  modal可见.value = false
}

function modal关闭动画结束() {
  切换文案()
}

function 重置数据() {
  重置设置()
  Message.success({ content: '已重置', duration: 1000 })
  // 关闭弹窗并通知重置
  setTimeout(() => {
    关闭弹窗()
    emits('reset')
  }, 300)
}

function 切换文案(id = '') {
  if (!id) {
    解释文案.value = ''
    return
  }

  let 文案主体 = 文案映射?.[id]
  // 快捷键的文案中包含动态文字, 需要替换
  const 需要替换文案的映射 = new Map([
    ['快捷键行为', replaceStr(文案主体, ['%s', 快捷键文案.value.copy])],
    ['语音朗读', replaceStr(文案主体, ['%s', 快捷键文案.value.read])],
  ])
  文案主体 = 需要替换文案的映射.get(id) || 文案主体
  解释文案.value = `<h2 class="text-20px text-#303133 dark:text-#fff">${id}:</h2>${文案主体}`
}

function 打开导入弹窗() {
  导入弹框Ref.value.打开导入弹窗()
}

interface importType {
  text: string
  password: string
}

async function 导入提交(data: importType) {
  try {
    await 导入配置(data.text, data.password)
    保存设置()
    Message.success('导入成功, 欢迎回来~🎉')
    导入弹框Ref.value.关闭导入弹窗()
    const 保存后的用户配置 = sortArraysInObject(getSettingToFromData())
    emits('ok', { 是否改动: !isEqual(保存后的用户配置, 设置备份.value) })
    关闭弹窗()
  }
  catch {
    Message.error('导入出错了, 可能是配置信息有误或密码错误😯')
  }
}

function 打开导出弹窗() {
  导出弹框Ref.value.打开导出弹窗()
}

async function 导出提交(password: string) {
  try {
    const 导出内容: string = await 导出设置(password)
    await 复制(导出内容)
    Message.success('导出成功, 已将配置信息写入到剪切板~')
    导出弹框Ref.value.关闭导出弹窗()
  }
  catch (error) {
    Message.error('导出出错了, 稍后再试一下吧😯')
  }
}

function 打开命名设置弹窗() {
  modal取消()
  emits('openNameSetting')
}

function 清除最近翻译方向记录() {
  清除历史记录()
  Message.success('已清除最近翻译方向记录~')
  emits('更新语法树')
}

// 暴露弹窗的函数, 供父组件调用
defineExpose({
  打开弹窗,
  关闭弹窗,
  modal可见,
})
</script>

<style lang="scss" scoped>
@mixin api-icon-show() {
  padding-left: 4px;
  padding-right: 6px;

  .icon_text {
    max-width: 60px;
    opacity: 1;
  }
}

.icon_wrapper {
  @apply flex items-center space-x-4px rounded-4px h-28px p-0 transition-all-200 hover:(bg-#f2f3f5 dark:bg-#3b3b3c);

  &:hover {
    @include api-icon-show;
  }

  .icon_text {
    @apply overflow-hidden whitespace-nowrap delay-50 max-w-0 opacity-0 inline-block transition-cusbezier-400;
  }
}

// 用于直接展示用的icon, 直接就拥有hover的效果
.icon_show {
  @include api-icon-show;
}

// .cus-divide:deep(.arco-divider-text) {
//   @apply bg-#fcfcfc dark:bg-#29292a;
// }
// .setting_wrapper:deep(.arco-input-wrapper) {
//   background: rgba(var(--gray-2), 0.75);
// }
</style>
