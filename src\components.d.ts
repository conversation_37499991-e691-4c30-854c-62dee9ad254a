/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACascader: typeof import('@arco-design/web-vue')['Cascader']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACheckboxGroup: typeof import('@arco-design/web-vue')['CheckboxGroup']
    ADgroup: typeof import('@arco-design/web-vue')['Dgroup']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AImagePreviewGroup: typeof import('@arco-design/web-vue')['ImagePreviewGroup']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputPassword: typeof import('@arco-design/web-vue')['InputPassword']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOption: typeof import('@arco-design/web-vue')['Option']
    ApiIcon: typeof import('./components/apiIcon.vue')['default']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    AResizeBox: typeof import('@arco-design/web-vue')['ResizeBox']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    AutoBtn: typeof import('./components/autoBtn.vue')['default']
    CodeBg: typeof import('./components/codeBg.vue')['default']
    ColorfulBtn: typeof import('./components/colorfulBtn.vue')['default']
    CusFullModal: typeof import('./components/cusFullModal.vue')['default']
    CusSwitch: typeof import('./components/cusSwitch.vue')['default']
    CusTooltip: typeof import('./components/cusTooltip.vue')['default']
    ExportModal: typeof import('./components/SettingModal/Modal/ExportModal.vue')['default']
    FormItemWrapper: typeof import('./components/formItemWrapper.vue')['default']
    ImportModal: typeof import('./components/SettingModal/Modal/ImportModal.vue')['default']
    Loading: typeof import('./components/loading.vue')['default']
    LoadingIcon01: typeof import('./components/loadingIcon01.vue')['default']
    LoadingIcon02: typeof import('./components/loadingIcon02.vue')['default']
    LoadingIcon03: typeof import('./components/loadingIcon03.vue')['default']
    LoadingIcon04: typeof import('./components/loadingIcon04.vue')['default']
    LoadingIcon05: typeof import('./components/loadingIcon05.vue')['default']
    MimicryBtn: typeof import('./components/mimicryBtn.vue')['default']
    ModalTitleCloseBtn: typeof import('./components/modalTitleCloseBtn.vue')['default']
    QuestionTooltip: typeof import('./components/questionTooltip.vue')['default']
    ReadBtn: typeof import('./components/readBtn.vue')['default']
    Segmented: typeof import('./components/Segmented/index.vue')['default']
    SegmentedGroup: typeof import('./components/Segmented/SegmentedGroup.vue')['default']
    SegmentedItem: typeof import('./components/Segmented/SegmentedItem.vue')['default']
    SettingCard: typeof import('./components/settingCard.vue')['default']
    SettingModal: typeof import('./components/SettingModal/index.vue')['default']
    SmoothTransitionIcon: typeof import('./components/smoothTransitionIcon.vue')['default']
    ToolsBar: typeof import('./components/toolsBar.vue')['default']
  }
}
