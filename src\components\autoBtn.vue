<template>
  <button
    bg="hover:(#f2f3f5 dark:#3d3d3d) active:(#eff2fa dark:#424242)"
    class="grid-c aspect-ratio-square w-33px cursor-pointer select-none overflow-hidden rounded-t-2px transition-cusbezier-200"
    @click="handleClick"
  >
    <div
      class="aspect-ratio-square w-22px flex-c rounded-4px transition-cusbezier-200"
      :class="[active ? 'active_auto_btn_bg' : 'disabled_auto_btn_bg']"
    >
      <i
        class="text-16px !transition-all-200"
        :class="[active ? 'active_auto_btn' : 'disabled_auto_btn']"
      />
    </div>
  </button>
</template>

<script setup lang="ts">
defineProps<{
  active: boolean
}>()
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

function handleClick(ev: MouseEvent) {
  emit('click', ev)
}
</script>

<style lang="scss" scoped></style>
