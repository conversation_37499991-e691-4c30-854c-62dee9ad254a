<template>
  <a-modal
    v-model:visible="modal可见"
    modal-animation-name="zoom-img"
    :fullscreen="true"
    :unmount-on-close="true"
    body-class="p-0 overflow-hidden h-full"
    title-align="start"
    title="图片翻译"
    @open="打开model()"
    @cancel="modal取消()"
    @close="modal关闭动画结束()"
  >
    <template #title>
      <div class="h-44px horizontal-place-8px">
        <img src="/favicon.svg" width="24">
        <span>图片翻译</span>
      </div>
    </template>
    <div
      p="x-20px t-16px b-0px"
      class="h-full flex flex-col space-y-16px"
      @drop.prevent="图片拖拽($event)"
      @dragover.prevent
    >
      <div
        border="~ #f1f2f3 dark:#444"
        shadow="hover:[5px_5px_7px_#f7f7f7] dark:([9px_9px_18px_#232324] hover:[6px_6px_8px_#212121])"
        class="h-72px flex-c flex-col cursor-pointer select-none rounded-md transition-cusbezier-200 space-y-4px"
        @click="选择图片()"
      >
        <template v-if="输入了百度的信息">
          <p class="text-16px">
            点此, 或往这个页面拖拽图片, 均可上传
          </p>
          <p text="14px [var(--color-text-4)]" class="flex-c">
            <span> (不一定非要拖拽到这里) ; 依赖百度图片翻译</span>
            <a-link @click.stop="去图片翻译文档()">
              点此查看
            </a-link>
            <span>教程</span>
          </p>
        </template>

        <template v-else>
          <p class="text-16px">
            你还未输入
            <span class="text_important">百度翻译</span>
            的相关信息, 无法使用图片翻译
          </p>
          <div>
            <a-link @click.stop="去图片翻译文档()">
              查看如何开通百度图片翻译
            </a-link>
            <span>或者</span>
            <a-link @click.stop="去填写百度信息()">
              已有信息去填写
            </a-link>
          </div>
        </template>
      </div>

      <section
        class="grid grid-cols-2 flex-1 gap-16px overflow-x-visible overflow-y-hidden"
      >
        <div class="img_card">
          <Transition name="component-scale" mode="out-in">
            <div v-if="原图链接" class="operation_btn absolute right-6px top-6px">
              <MimicryBtn class="size-25px" @click="清除数据()">
                <i class="i-line-md-close" />
              </MimicryBtn>
            </div>
          </Transition>
          <a-image-preview-group
            v-model:current="current"
            v-model:visible="原图预览Vis"
            :src-list="[原图链接]"
          />
          <div v-if="原图链接" class="size-full flex-c p-6px">
            <img
              :src="原图链接"
              class="size-full object-contain"
              @click="原图预览()"
            >
          </div>
          <CodeBg v-else text="原图" />
        </div>
        <div class="img_card relative">
          <Transition name="component-scale" mode="out-in">
            <div v-if="显示复制按钮条件" class="operation_btn absolute right-6px top-6px">
              <MimicryBtn class="h-25px w-25px" @click="复制结果()">
                <i class="i-mingcute-copy-2-line" />
              </MimicryBtn>
            </div>
          </Transition>
          <TransitionGroup name="custom-fade">
            <!-- Loading -->
            <Loading v-if="图片翻译加载" key="loading" class="absolute inset-0 z-100" />

            <!-- 图片结果的元素 -->
            <template v-if="翻译结果数据 && 翻译结果模式 === 'image' && 翻译结果数据.img">
              <div key="图片预览" class="contents">
                <a-image-preview-group
                  v-model:current="current"
                  v-model:visible="结果预览Vis"
                  :src-list="[翻译结果数据.img]"
                />
              </div>
              <div class="size-full flex-c p-6px">
                <img
                  key="图片结果本体"
                  :src="翻译结果数据.img"
                  class="size-full object-contain"
                  @click="结果预览()"
                >
              </div>
            </template>

            <!-- 文字结果的元素 -->
            <template v-else-if="翻译结果数据 && 翻译结果模式 === 'text'">
              <p key="文字结果本体" class="p-6px">
                {{ 翻译结果数据.text }}
              </p>
            </template>

            <!-- 背景文字 -->
            <CodeBg v-else key="card背景" text="结果" />
          </TransitionGroup>
        </div>
      </section>
    </div>
    <template #footer>
      <div class="image_footer flex justify-between text-left">
        <div class="flex items-center space-x-8px">
          <a-cascader
            v-model:model-value="form和to的数组"
            path-mode
            :options="语种树的数据"
            :style="{
              width: '220px',
              height: '32px',
            }"
            value-key="id"
            :format-label="格式化级联显示内容"
            @change="重新翻译()"
          />
          <div class="text-20px">
            <CusTooltip content="去截图翻译" @click="截图并翻译()">
              <a-button class="icon_btn h-32px">
                <div class="i-bx-screenshot mt-1px text-19px" />
              </a-button>
            </CusTooltip>
          </div>
        </div>

        <div class="flex items-center space-x-8px">
          <a-radio-group v-model="翻译结果模式">
            <a-radio value="image">
              图片结果
            </a-radio>
            <a-radio value="text">
              文字结果
            </a-radio>
          </a-radio-group>
          <a-button class="space-x-4px" @click="保存图片()">
            <i class="i-ic-round-save mt-0.5px text-16px" />
            <span>保存图片</span>
          </a-button>
          <a-button status="danger" @click="重新翻译()">
            重新翻译
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { 级联值类型 } from '@MainView/MainViewTypes'
import { 通用翻译 } from '@/apis/translation/index'
import { 用户配置存储 } from '@/store/useUserSetting'
import { showMainWindow } from '@/utils/utools'
import { Message } from '@arco-design/web-vue'
import { 返回语种树 } from '@MainView/MainViewData'
import { 格式化级联显示内容 } from '@MainView/MainViewModule'

const emits = defineEmits(['goBaidu'])
// 复制结果功能
const 图片翻译加载 = ref(false)
const { copy: 复制 } = useClipboard()
const 语种树的数据 = ref(返回语种树())
const 翻译结果模式 = ref('image')
const { getKeyByTag } = 用户配置存储()
const 输入了百度的信息 = computed(() => {
  const { appid, token } = getKeyByTag('baidu')
  return appid && token
  // console.log("🚀 ~ file: ImageTranslate.vue:153 ~ keyData >>>", keyData)
  // return false
})
const form和to的数组 = ref<级联值类型>(['auto', 'zh'])
const modal可见 = ref(false)
const 原图链接 = ref('')
const 原图预览Vis = ref(false)
const 结果预览Vis = ref(false)
const current = ref(0)
const 翻译结果数据 = ref<any>(null)

function 去图片翻译文档() {
  openUrl(
    'https://flowus.cn/4739fa73-4333-475b-a1e9-d6287eda3b26#0d2cc1c1-c6d3-4e3c-9c40-46cecb97c02a',
  )
}

function 去填写百度信息() {
  modal取消()
  emits('goBaidu')
}

function 原图预览() {
  原图预览Vis.value = true
}

function 结果预览() {
  结果预览Vis.value = true
}
// async function 复制结果() {
//   switch (翻译结果模式.value) {
//     case 'image': {
//       if(翻译结果数据.value?.img){
//         window.servers.copyImg(翻译结果数据.value?.img)
//         Message.success('复制图片结果成功')
//         break
//       } else {
//         Message.error('无图片')
//       }
//     }
//     case 'text': {
//       if(翻译结果数据.value?.text){
//         await 复制(翻译结果数据.value?.text)
//         Message.success('复制文字结果成功')
//         break
//       }else {
//         Message.error('无文字')
//       }
//     }
//   }
// }

async function 复制结果() {
  const 翻译结果模式值 = 翻译结果模式.value
  const 翻译结果数据值 = 翻译结果数据.value

  const 操作: any = {
    image: () => {
      if (翻译结果数据值?.img) {
        window.servers?.copyImg(翻译结果数据值.img)
        Message.success('复制图片结果成功')
      }
      else {
        Message.error('无图片')
      }
    },
    text: async () => {
      if (翻译结果数据值?.text) {
        await 复制(翻译结果数据值.text)
        Message.success('复制文字结果成功')
      }
      else {
        Message.error('无文字')
      }
    },
  }

  操作?.[翻译结果模式值]?.()
}

async function 选择图片() {
  if (!输入了百度的信息.value) {
    return
  }
  浏览器选图()
  // if (window.utools) {
  //   uTools选图()
  // } else {
  //   浏览器选图()
  // }
}

/**
 * TODO: utools选图后格式为Buffer, 需要处理
 * 暂时不用utools选图, 后续尝试继续
 */
// function uTools选图() {
//   // 选择图片
//   const path = window.servers.openFileDialog()[0]
//   const img = window.servers.readImgFile(path)
//   开始翻译(img)
//   originSrc.value = path
//   srcList.value[0] = path
//   sendTrans(img)
// }

const 文件系统 = useFileSystemAccess({
  dataType: 'Blob',
  types: [
    {
      description: '只能选一张图',
      accept: {
        '*/.jpg': ['.jpg', '.jpeg'],
        '*/.png': ['.png'],
      },
    },
  ],
  excludeAcceptAllOption: true,
})
function bytes转KB(bytes: number) {
  return bytes / 1024
}
const 体积限制 = ref(1024 * 4)
const 超出体积的提示 = '请将图片大小控制在4MB以内'
async function 浏览器选图() {
  try {
    await 文件系统.open()
    showMainWindow()
    if (bytes转KB(文件系统.fileSize.value) > 体积限制.value) {
      return Message.error(超出体积的提示)
    }
    if (文件系统.file.value) {
      原图链接.value = blobToUrl(文件系统.file.value)
      开始翻译(文件系统.file.value)
    }
  }
  catch {}
}

function 检查百度信息填写() {
  if (!输入了百度的信息.value) {
    Message.error('请先填写百度翻译的信息')
    return false
  }
  return true
}

async function 图片拖拽(e: any) {
  if (!检查百度信息填写()) {
    return
  }
  try {
    const file = e.dataTransfer.files[0]
    if (bytes转KB(file.size) > 体积限制.value) {
      return Message.error(超出体积的提示)
    }
    if (!/\.jpe?g$|\.png$/i.test(file.name)) {
      return Message.error('只能上传jpg或png格式的图片')
    }
    const blob = await fileToBlob(file)
    原图链接.value = blobToUrl(blob)
    开始翻译(blob)
  }
  catch (err) {
    Message.error('无法读取该图片')
  }
}

function modal取消() {
  modal可见.value = false
  清除数据()
}

function cleanBase64(base64Data: string) {
  return base64Data.replace(/^data:image\/(png|jpg|jpeg);base64,/, '')
}

/**
 * Converts a base64 string into a Blob.
 *
 * @param b64Data The base64 string you want to convert.
 * @param contentType The content type of the Blob. This is optional and defaults to ''.
 * @param sliceSize The size of each slice. This is optional and defaults to 512.
 * @return A Blob representing the input base64 string.
 */
function base64ToBlob(b64Data: string, contentType = '', sliceSize = 512): Blob {
  const byteCharacters = atob(cleanBase64(b64Data))
  const byteArrays: Uint8Array[] = []

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize)

    const byteNumbers: number[] = Array.from({ length: slice.length })
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }

    const byteArray = new Uint8Array(byteNumbers)
    byteArrays.push(byteArray)
  }

  const blob = new Blob(byteArrays, { type: contentType })
  return blob
}

function 打开model(imgUrl?: string) {
  // console.log('打开model:', imgUrl)
  modal可见.value = true
  通过base64图片翻译(imgUrl)
  // if (imgUrl) {
  //   原图链接.value = imgUrl
  //   // 将base64编码转换为Blob格式
  //   const contentType = 'image/png'
  //   const blob = base64ToBlob(imgUrl, contentType)
  //   开始翻译(blob)
  // }
}

function 通过base64图片翻译(imgUrl?: string) {
  if (imgUrl) {
    原图链接.value = imgUrl
    // 将base64编码转换为Blob格式
    const contentType = 'image/png'
    const blob = base64ToBlob(imgUrl, contentType)
    开始翻译(blob)
  }
}

function modal关闭动画结束() {}

const lastFile = ref<any>(null)
async function 开始翻译(file: Blob) {
  if (!检查百度信息填写()) {
    return
  }
  lastFile.value = file
  翻译结果数据.value = null
  图片翻译加载.value = true
  const apiRes = await 通用翻译('baiduImg', {
    file,
    from: form和to的数组.value[0],
    to: form和to的数组.value[1],
  })
  图片翻译加载.value = false
  if (apiRes.code !== 200) {
    apiRes.img = 原图链接.value
    //  翻译结果模式.value = 'text'
    Message.error(apiRes.text)
  }
  翻译结果数据.value = apiRes
}

function 重新翻译() {
  if (lastFile.value) {
    开始翻译(lastFile.value)
  }
}

function 保存图片() {
  if (翻译结果数据.value?.img) {
    const state = window.servers.saveImg(翻译结果数据.value.img)
    if (state) {
      Message.success('保存成功')
    }
  }
  else {
    Message.error('无图片')
  }
}

async function 截图并翻译() {
  const base64Res = await window.servers.screenCapture()
  通过base64图片翻译(base64Res)
}

function 清除数据() {
  lastFile.value = null
  原图链接.value = ''
  翻译结果数据.value = null
}

const 显示复制按钮条件 = computed(() => {
  return (
    (翻译结果模式.value === 'text' && 翻译结果数据.value?.text)
    || (翻译结果模式.value === 'image' && 翻译结果数据.value?.img)
  )
})

defineExpose({
  打开model,
  modal取消,
  截图并翻译,
})

// defineOptions({
//   name: 'ImageTranslate',
// })
</script>

<style lang="scss" scoped>
.img_card {
  @apply h-[calc(100%-16px)] overflow-hidden border-1px relative transition-cusbezier-300 rounded-md border-#f2f3f4 shadow-[0_4px_8px_1px_#064bb30a]  hover:(shadow-[5px_5px_7px_#f7f7f7] dark:shadow-[6px_6px_8px_#212121]) dark:(border-#444 shadow-[9px_9px_18px_#232324]);

  .operation_btn {
    @apply op-30 transition-cusbezier-200;
  }

  &:is(:hover) {
    .operation_btn {
      opacity: 1;
    }
  }

}

.icon_btn {
  @apply px-4px flex-c;
}
</style>
