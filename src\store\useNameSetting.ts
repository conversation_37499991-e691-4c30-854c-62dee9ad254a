/** 命名模式配置 */
import { defineStore } from 'pinia'
import { 切换类型数组 as 命名翻译类型数组 } from '@/assets/changeCaseMap'
import { getDbStorageItem, removeDbStorageItem, setDbStorageItem } from '@/utils/storage'
import {
  为本插件应用动态新增某个功能,
  动态删除本插件应用的某个功能,
} from '@/utils/utools'

const utools = window?.utools

export const 命名模式配置存储 = defineStore('nameSetting', () => {
  const 命名翻译设置数据 = ref<any[]>([])

  function 读取系统默认配置() {
    return 命名翻译类型数组.map((item) => {
      return {
        name: item.name,
        remark: item.remark,
        label: item.label,
        enable: true,
        keyEnable: true,
        anyKeyEnable: false,
        keyConfig: item.keyConfig,
      }
    })
  }

  function 初始化设置数据() {
    const config = getDbStorageItem('namingModeConfig', null)
    const 系统默认配置 = 读取系统默认配置()

    if (config) {
      // TODO: 合并系统默认配置和用户配置
      const 系统Map = new Map()
      系统默认配置.forEach((item: any) => {
        系统Map.set(item.name, item)
      })

      const temoData: any[] = []
      config.forEach((item: any) => {
        const 系统配置项 = 系统Map.get(item.name)
        const keyConfig = 系统配置项 ? 系统配置项.keyConfig : null
        if (keyConfig) {
          temoData.push({
            ...item,
            keyConfig,
          })
        }
      })

      命名翻译设置数据.value = temoData
    }
    else {
      命名翻译设置数据.value
      = 系统默认配置
    }

    // 保存数据()
  }

  function 保存数据(datas?: any[]) {
    if (datas) {
      命名翻译设置数据.value = datas
    }
    setDbStorageItem('namingModeConfig', 命名翻译设置数据.value)
    设置命名关键字()
  }

  // 重置所有设置
  function reset() {
    removeDbStorageItem('namingModeConfig')
    初始化设置数据()
    保存数据()
    设置命名关键字()
  }

  function 设置命名关键字() {
    if (!utools) {
      return
    }
    命名翻译设置数据.value.forEach((item: any) => {
      const { keyEnable, anyKeyEnable } = item
      const tempKeyConfig = item.keyConfig

      const code = tempKeyConfig?.code || ''
      const keyConfig = {
        code: tempKeyConfig.code,
        explain: tempKeyConfig.explain,
        label: tempKeyConfig.label,
        cmds: [] as any[],
      }
      if (tempKeyConfig && keyEnable) {
        let cmds: any[] = []
        // 普通关键字
        if (keyEnable) {
          cmds = tempKeyConfig.cmds || []
        }
        // 空心关键字
        if (anyKeyEnable) {
          cmds = [...tempKeyConfig.cmds, ...tempKeyConfig.anyCmds]
        }

        keyConfig.cmds = cmds

        动态删除本插件应用的某个功能(code)
        为本插件应用动态新增某个功能(keyConfig)
      }
      if (!keyEnable) {
        动态删除本插件应用的某个功能(code)
      }
    })

    // 移除脏数据，3个版本后可移除此代码
    动态删除本插件应用的某个功能('codeMode__trainCase')
    动态删除本插件应用的某个功能('codeMode__kebabCase')
    动态删除本插件应用的某个功能('codeMode__titleCase')
  }

  初始化设置数据()
  设置命名关键字()

  return {
    命名翻译设置数据,
    保存数据,
    reset,
  }
})
