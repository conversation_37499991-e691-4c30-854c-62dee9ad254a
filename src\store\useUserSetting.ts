/** 用户配置信息 */
import { defineStore } from 'pinia'
import { cloneDeep } from 'lodash-es'
import { clearOldVersionStorage, getOldVersionStorage } from './oldUserSetting'
import { getDbStorageItem, removeDbStorageItem, setDbStorageItem } from '@/utils/storage'
import { apiOptions, type 语种 } from '@/assets/translateApiOption'
import { 切换类型数组 } from '@/assets/changeCaseMap'

export class BaseConfigType {
  [key: string]: any
  // 首页翻译
  homeOption: string[] = getDefaultHomeApi()
  // 默认使用的api
  defaultApi: string = getDefaultHomeApi()[0]
  // 字体大小
  fontSize = 16
  // 复制快捷键行为
  copyBtnBehavior = 'open'
  // 命名翻译模式
  codeMode = false
  // 命名翻译模式选项
  codeModeOption: string[] = getDefaultCodeOption()
  // 显示的复制按钮
  copyBtnShow: number[] = [1, 2, 3]
  // 语音朗读
  readAloud = true
  // 朗读偏好
  readingPreference = 'default'
  // 插件主题
  theme = 'auto'
  // 首选译文语言语种
  defaultForeignLanguage: 语种 = 'en'
  // 语言模式(在线|离线)
  readingModel = '在线'
  // 去除换行符
  removeN = false
  // 三次退格清空
  tripleBackspaceClear = false
  // 翻译触发方式
  translationTriggerMode = 'input'
  // 最近翻译方向存储数量
  lastDirectionStorageNum = 6
}

export interface keyConfigType {
  [key: string]: any
}
export interface 首页api类型 {
  value: string
  label: string
}

/** 获取默认的首页api */
function getDefaultHomeApi() {
  return apiOptions.slice(0, 4).map(i => i.value)
}

/** 获取默认的命名翻译选项 */
function getDefaultCodeOption() {
  return 切换类型数组.map(i => i.name)
}

export const 用户配置存储 = defineStore('userSetting', () => {
  // 基本配置
  function initializationBaseConfig() {
    return new BaseConfigType()
  }
  const baseConfig: BaseConfigType = reactive({ ...initializationBaseConfig() })
  Object.assign(baseConfig, initBaseConfig())
  /** 获取首页api选择 */
  const getHomeApiOptions = computed((): 首页api类型[] => {
    const tempMap = new Map(cloneDeep(apiOptions).map(i => [i.value, i]))
    return baseConfig.homeOption.map((key: string) => {
      return tempMap.get(key)!
    })
    // return baseConfig.homeOption.map((key: string) => {
    //   return tempMap.get(key)
    // })
  })

  function initBaseConfig() {
    let config = getDbStorageItem('baseConfig', {})

    let loadOld = false
    if (Object.keys(config).length === 0) {
      // 配置不存在, 尝试获取旧版版存储
      // console.log('配置不存在, 尝试获取旧版存储')
      config = getOldVersionStorage()
      loadOld = true
    }

    if (loadOld && Object.keys(config).length !== 0) {
      // 旧版有配置, 迁移旧版数据至新版
      // console.log('旧版有配置, 迁移旧版数据至新版')
      const newConfig = new BaseConfigType()
      Object.keys(config).forEach((key: string) => {
        newConfig[key] = config[key]
      })
      config = newConfig
      saveBaseConfig(config)

      // 迁移完成, 删除旧版配置数据
      // console.log('迁移完成, 删除旧版配置数据')
      clearOldVersionStorage()
    }

    return config
  }

  // 保存基本配置
  function saveBaseConfig(configData: object) {
    Object.assign(baseConfig, configData)
    setDbStorageItem('baseConfig', baseConfig)
  }

  // 保存指定key的基本配置
  function saveBaseConfigByKey<T extends keyof BaseConfigType>(key: T, setData: any) {
    baseConfig[key] = setData
    setDbStorageItem('baseConfig', baseConfig)
  }

  // keys配置
  const keyConfig = ref<keyConfigType>({})
  keyConfig.value = getDbStorageItem('keyConfig', {})

  const xiaoniuApiKey = computed(() => keyConfig.value?.xiaoniu?.xiaoniuApiKey)

  // 保存key配置
  function saveKeyConfig(configDara: any) {
    keyConfig.value = configDara
    setDbStorageItem('keyConfig', keyConfig.value)
  }

  /**
   * 获取指定密钥
   * @param {string} tag 第三方api标识
   */
  function getKeyByTag(tag: string) {
    return keyConfig.value[tag] || {}
  }

  // 重置所有设置
  function reset() {
    removeDbStorageItem('baseConfig')
    removeDbStorageItem('keyConfig')
    Object.assign(baseConfig, initializationBaseConfig())
    keyConfig.value = {}
  }

  return {
    baseConfig,
    getHomeApiOptions,
    saveBaseConfig,
    saveBaseConfigByKey,
    keyConfig,
    saveKeyConfig,
    getKeyByTag,
    reset,
    xiaoniuApiKey,
  }
})
