import { resolve } from 'node:path'
import process from 'node:process'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ArcoResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import obfuscatorPlugin from 'vite-plugin-javascript-obfuscator'
import VueDevTools from 'vite-plugin-vue-devtools'
import { primaryColor } from './src/config/colorConfig'

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir)
}

export default defineConfig({
  base: './',
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: { 'arcoblue-6': primaryColor },
        javascriptEnabled: true,
      },
      // scss: {
      //   api: 'modern-compiler', // or "modern", "legacy"
      // },
    },
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ArcoResolver()],
      imports: ['vue', '@vueuse/core', 'pinia'],
      dts: 'src/auto-imports.d.ts',
      dirs: ['src/utils/common/'],
      vueTemplate: true,
    }),
    UnoCSS(),
    Components({
      resolvers: [ArcoResolver()],
      dts: 'src/components.d.ts',
    }),
    VueDevTools(),
    obfuscatorPlugin({
      include: [/\.(jsx?|tsx?|cjs|mjs|vue)$/],
      apply: 'build',
      options: {
        // 配置详见: https://github.com/javascript-obfuscator/javascript-obfuscator#compact
        controlFlowFlattening: true, // 启用代码控制流扁平化。控制流扁平化是源代码的结构转换, 它阻碍了程序的理解。
        controlFlowFlatteningThreshold: 0.4, // 给定任意节点应用 controlFlowFlattening 转换的概率
        deadCodeInjection: true, // 将会在混淆代码中添加随机的死代码块
        deadCodeInjectionThreshold: 0.4, // 设置受死代码注入影响的节点百分比
        identifierNamesGenerator: 'hexadecimal', // 生成类似 _0xabc123 的标识符名称
        simplify: true, // 通过简化代码实现代码混淆
        splitStrings: true, // 将字面字符串分割成长度为指定长度的拼接形式
        splitStringsChunkLength: 10, // 设置 splitStrings 选项的块长度。
        stringArrayCallsTransform: true, // 启用字符串数组调用转换。根据 stringArrayCallsTransformThreshold 值, 这些调用的所有参数都可能被提取为不同的对象
        stringArrayCallsTransformThreshold: 0.5, // 调整 stringArrayCallsTransform 的概率
        stringArrayEncoding: ['base64'], // 使用 base64 对 stringArray 的所有字符串字面值进行编码
        stringArrayWrappersCount: 2, // 设置 stringArray 在每个根作用域或函数作用域中的封装数
        stringArrayWrappersParametersMaxCount: 4, // 允许控制 stringArray 包装器参数的最大数量
        stringArrayWrappersType: 'function', // 在每个作用域内的随机位置添加函数包装器
        stringArrayThreshold: 0.6, // 调整字符串文字插入到 stringArray 中的概率
        transformObjectKeys: true, // 启用对象键的转换
      },
    }),
  ],
  resolve: {
    alias: {
      '@': pathResolve('src'),
      '@imgs': pathResolve('src/assets/imgs'),
      '@MainView': pathResolve('src/views/MainView'),
    },
  },
  build: {
    // 使用esbuild压缩
    // target: 'esnext',
    // minify: 'esbuild',

    // 使用terser压缩
    target: 'es2021',
    minify: 'terser',
    terserOptions: {
      ecma: 2020,
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          dep: [
            '@vueuse/core',
            'change-case',
            'dayjs',
            'lodash-es',
            'nanoid',
            'pinia',
            'qs',
            'vue',
          ],
          arco: ['@arco-design/web-vue'],
        },
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 6789,
    open: true,
    proxy: {
      // 百度翻译
      '/baiduApi': {
        target: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/baiduApi/, ''),
      },
      // 腾讯翻译
      '/tencentApi': {
        target: 'https://tmt.tencentcloudapi.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/tencentApi/, ''),
      },
      // 有道翻译
      '/youdaoApi': {
        target: 'http://openapi.youdao.com/api',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/youdaoApi/, ''),
      },
      // 彩云小译
      '/caiyunApi': {
        target: 'http://api.interpreter.caiyunai.com/v1/translator',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/caiyunApi/, ''),
      },
      // 阿里翻译
      '/aliApi': {
        target: 'http://mt.cn-hangzhou.aliyuncs.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/aliApi/, ''),
      },
      // 火山翻译
      '/huoshanApi': {
        target: 'https://open.volcengineapi.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/huoshanApi/, ''),
      },
      // 接口
      '/uniapi': {
        target: 'https://be392405-5b88-4143-ad6b-f155b106ab85.bspapp.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/uniapi/, ''),
      },
      // 小牛翻译
      '/xiaoniu': {
        target: 'https://api.niutrans.com/NiuTransServer/translation',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/xiaoniu/, ''),
      },
      // 腾讯交互
      '/tencentTransmart': {
        target: 'https://transmart.qq.com/api/imt',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/tencentTransmart/, ''),
      },
      // deepl free
      '/deepl_free': {
        target: 'https://api-free.deepl.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/deepl_free/, ''),
      },
      // deepl free
      '/deepl_pro': {
        target: 'https://api.deepl.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/deepl_pro/, ''),
      },
      '/web_time': {
        target: 'http://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/web_time/, ''),
      },
      // 百度图片翻译
      '/baiduImg': {
        target: 'https://fanyi-api.baidu.com/api/trans/sdk/picture',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/baiduImg/, ''),
      },
      // tts
      '/tts': {
        target: 'https://ef-tts-cf.eeff.fun',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/tts/, ''),
      },
    },
  },
})
