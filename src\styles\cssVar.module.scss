// 全局变量
@use 'sass:color';
@use 'sass:list';

$primary: #5b61ff; // 浅色模式主色
$primary-dark: #6168fc; // 深色模式主色
$code-light-final: #f5f6f9; // 浅色模式下<code/>的最终颜色
$code-dark-primary: #fff; // 深色模式下<code/>的主色
$code-dark-final: #414141; // 深色模式下<code/>的最终颜色

@function hex-to-rgb($hex, $use-comma: true) {
  $channels: "red" "green" "blue";
  $rgb: ();

  @each $channel in $channels {
    $rgb: list.append($rgb, color.channel($hex, $channel, $space: rgb));
  }

  $separator: if($use-comma, ", ", " ");

  @return #{list.nth($rgb, 1)}#{$separator}#{list.nth($rgb, 2)}#{$separator}#{list.nth($rgb, 3)};
}

:root {
  --primary-color: #{$primary}; // 主色
  --primary-rgb-color: #{hex-to-rgb($primary)}; // 主色的RGB值(逗号分隔, 形如「91,97,255」)
  --primary-rgb-color-without-comma: #{hex-to-rgb($primary,false)}; // 主色的RGB值(空格分隔, 形如「91 97 255」用于新的rgb语法)
  --code-0-stroke: rgba(#{hex-to-rgb($primary)}, 0.6); // 代码的描边颜色0%
  --code-50-stroke: rgba(#{hex-to-rgb($primary)}, 0.3); // 代码的描边颜色50%
  --code-70-stroke: #{$code-light-final}; // 代码的描边颜色70%
  --code-90-color: #{$code-light-final}; // 代码的填充颜色90%
  --tools-bar-bottom-color: #fff; // 工具栏底部的颜色
  --ani-bezier: cubic-bezier(0.22, 0.58, 0.12, 0.98); // 动画曲线
  --main-textarea-border-color: #e9e9e9; // 主文本框的边框颜色
  --main-textarea-border-focus-color: #{$primary}; // 主文本框的边框focus颜色
  --setting-header-color: #fff; // 设置面板的头部颜色
  --mimicry-btn-color: #fff; // 拟态按钮初始颜色, 因为是纯色, 所以不分A和B
  --mimicry-btn-shadow-color-a: #d9d9d9; // 拟态按钮的阴影颜色A
  --mimicry-btn-shadow-color-b: #fff; // 拟态按钮的阴影颜色B
  --mimicry-btn-interactive-color-a: #eee; // 拟态按钮的交互颜色A
  --mimicry-btn-interactive-color-b: #fff; // 拟态按钮的交互颜色B
  --colorful-btn-color-a: #ff000080; // 彩色按钮的颜色A
  --colorful-btn-color-b: #ffff0080; // 彩色按钮的颜色B
  --colorful-btn-color-c: #00ffff80; // 彩色按钮的颜色C
  --colorful-btn-color-d: #0000ff80; // 彩色按钮的颜色D
  --colorful-btn-text-shadow: none; // 彩色按钮的文字阴影
  --main-bg-color-01: #f4f4f4; // 主背景颜色01
  --main-bg-color-02: transparent; // 主背景颜色02
  --setting-small-modal-shadow-color: #444; // 设置面板中的导入/导出弹窗的阴影颜色
  --main-font-family: 'Inter', 'HarmonyOS Sans SC Medium', 'HarmonyOS_Medium', 'Noto Sans KR', 'Noto Sans Thai', 'Noto Sans SC', system-ui, segoe ui, roboto, helvetica, arial, sans-serif;
  --code-font-family: 'JetBrains Mono', 'Cascadia Mono', menlo, monaco, consolas, 'HarmonyOS Sans SC Medium', 'HarmonyOS_Medium';
  --jp-font: 'M PLUS 2', var(--main-font-family);
}

:root[class~='dark'] {
  --primary-color: #{$primary-dark}; // 主色
  --primary-rgb-color: #{hex-to-rgb($primary-dark)}; // 主色的RGB值(逗号分隔, 形如「91,97,255」)
  --code-0-stroke: rgba(#{hex-to-rgb($code-dark-primary)}, 0.9); // 代码的描边颜色0%
  --code-50-stroke: rgba(#{hex-to-rgb($code-dark-primary)}, 0.4); // 代码的描边颜色50%
  --code-70-stroke: rgba(#{hex-to-rgb($code-dark-primary)}, 0.1); // 代码的描边颜色70%
  --code-90-color: #{$code-dark-final}; // 代码的填充颜色90%
  --tools-bar-bottom-color: #303133; // 工具栏底部的颜色
  --main-textarea-border-color: transparent; // 主文本框的边框颜色
  --main-textarea-border-focus-color: #777; // 主文本框的边框focus颜色
  --setting-header-color: #2a2a2b; // 设置面板的头部颜色
  --mimicry-btn-color: #333; // 拟态按钮初始颜色, 因为是纯色, 所以不分A和B
  --mimicry-btn-shadow-color-a: #112; // 拟态按钮的阴影颜色A
  --mimicry-btn-shadow-color-b: #343434; // 拟态按钮的阴影颜色B
  --mimicry-btn-interactive-color-a: #222; // 拟态按钮的交互颜色A
  --mimicry-btn-interactive-color-b: #222; // 拟态按钮的交互颜色B
  --colorful-btn-color-a: #1900ff77; // 彩色按钮的颜色A
  --colorful-btn-color-b: #06f7; // 彩色按钮的颜色B
  --colorful-btn-color-c: #5e4dff77; // 彩色按钮的颜色C
  --colorful-btn-color-d: #a200ff77; // 彩色按钮的颜色D
  --colorful-btn-text-shadow: 0 2px 12px #ffffff6e; // 彩色按钮的文字阴影
  --setting-small-modal-shadow-color: #090909; // 设置面板中的导入/导出弹窗的阴影颜色
  --main-bg-color-01: transparent; // 主背景颜色01
  --main-bg-color-02: transparent; // 主背景颜色02
}
