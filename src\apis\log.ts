import axios from 'axios'
import { userStore } from '@/store/useUser'

/**
 * 上传日志
 * @param tag 日志标识
 */
export function 上传日志(tag: string, remark?: string) {
  return
  const baseUrl = import.meta.env.VITE_UNIAPI_BASEURL
  if (!import.meta.env.DEV) {
    const user = userStore()
    const { userInfo } = user

    axios(`${baseUrl}/log`, {
      params: {
        tag,
        remark,
        user: userInfo.open_id ? JSON.stringify(user.userInfo) : 'null',
      },
    })
  }
}

/**
 * 上传开发日志
 * @param tag 日志标识
 */
export function 上传开发日志(tag: string, remark?: string) {
  const baseUrl = import.meta.env.VITE_UNIAPI_BASEURL
  axios(`${baseUrl}/devLog`, { params: { tag, remark } })
}
