/** 一些旧版用户存储资料(要用) */
import { getDbStorageItem, removeDbStorageItem } from '@/utils/storage'

const oldKeys = [
  {
    key: 'homeOption',
    defaultState: [],
  },
  {
    key: 'defaultApi',
    defaultState: '',
  },
  {
    key: 'fontSize',
    defaultState: 16,
  },
  {
    key: 'copyBtnBehavior',
    defaultState: 'open',
  },
  {
    key: 'copyBtnShow',
    defaultState: [1, 2, 3],
  },
  {
    key: 'codeMode',
    defaultState: false,
  },
  {
    key: 'codeMode',
    defaultState: false,
  },
  {
    key: 'readAloud',
    defaultState: true,
  },
  {
    key: 'readingPreference',
    defaultState: 'default',
  },
  {
    key: 'theme',
    defaultState: 'auto',
  },
  {
    key: 'defaultForeignLanguage',
    defaultState: 'en',
  },
  {
    key: 'readingModel',
    defaultState: '在线',
  },
  {
    key: 'removeN',
    defaultState: false,
  },
  {
    key: 'tripleBackspaceClear',
    defaultState: false,
  },
]

// 获取旧版本的存储
export function getOldVersionStorage() {
  const oldConfig: any = {}
  oldKeys.forEach((keyItem) => {
    const tempStr = getDbStorageItem(keyItem.key)
    if (tempStr) {
      const defaultValue = keyItem.defaultState
      const typeStr = typeof defaultValue
      let val
      if (typeStr === 'number') {
        val = Number.parseFloat(tempStr)
      }
      else if (typeStr === 'object') {
        val = JSON.parse(tempStr)
      }
      else if (typeStr === 'boolean') {
        val = !!(tempStr === 'true' || false)
      }
      else {
        val = tempStr
      }
      oldConfig[keyItem.key] = val
    }
  })
  return oldConfig
}

// 清除旧版本存储数据
export function clearOldVersionStorage() {
  oldKeys.forEach((keyItem) => {
    // const tempStr = getDbStorageItem(keyItem.key)
    removeDbStorageItem(keyItem.key)
  })
}
