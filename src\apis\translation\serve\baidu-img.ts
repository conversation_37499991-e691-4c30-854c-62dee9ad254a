/**
 * 百度翻译接口
 * https://fanyi-api.baidu.com/doc/21
 */

import { type 翻译参数Type, 返回状态码及信息 } from '../common'

const 错误信息: Record<string, string> = {
  52001: '请求超时, 请重试',
  52002: '服务端系统错误, 请重试',
  52003: '未授权用户, 请检查appid是否正确或者服务是否开通',
  52010: '开放设备授权容量不足, 联系管理员扩增容量',
  54000: '必填参数为空或固定参数有误, 检查参数是否误传',
  54001: '签名错误, 请检查您的签名生成方法',
  54003: '访问频率受限, 请降低您的调用频率',
  54004: '账户余额不足, 请前往管理控制平台为账户充值',
  54005: '长query请求频繁, 请降低长query的发送频率, 3s后再试',
  58000:
    '客户端IP非法, 检查个人资料里填写的IP地址是否正确可前往管理控制平台修改, IP限制, IP可留空',
  58001: '译文语言方向不支持, 检查译文语言是否在语言列表里',
  69001: '上传图片数据有误, 检查图片是否有问题',
  69002: '图片识别超时, 请重试',
  69003: '内容识别失败, 检查图片是否存在内容后重试',
  69004: '识别内容为空, 检查图片是否存在内容后重试',
  69005: '图片大小超限 (超过4M) , 请上传符合图片大小要求的图片',
  69006:
    '图片尺寸不符合标准 (最短边至少30px, 最长边最大4096px) , 请上传符合图片尺寸要求的图片',
  69007: '图片格式不支持 (png/jpg) , 请上传png或jpg格式的图片',
  69008: '设备号为空, 检查cuid参数',
  69012:
    '文字贴合参数异常, 请检查参数 paste, 枚举示例：0-关闭文字贴合 1-返回整图贴合 2-返回块区贴合',
  58002: '服务当前已关闭, 请前往管理控制台开启服务',
  90107: '认证未通过或未生效, 请前往我的认证查看认证进度',
}

/**
 * 通用翻译
 * @param {string} options.file 请求翻译图片
 * @param {string} options.from 翻译源语言(可设置为auto)
 * @param {string} options.to 翻译目标语言 (可设置为auto)
 * @param {object} options.keyConfig key配置
 */
export async function imgTranslation({ from, to, keyConfig, file }: 翻译参数Type) {
  if (window.servers) {
    const params = {
      from,
      to,
      file,
    }

    try {
      const { data: apiRes } = await window.servers.transImg(params, keyConfig)
      // console.log('图片翻译结果:')
      const { error_code, error_msg, data } = apiRes
      const numErrorCode = Number.parseInt(error_code)
      if (numErrorCode === 0) {
        return 返回状态码及信息(200, {
          text: data.sumDst,
          img: `data:image/png;base64,${data.pasteImg}`,
          data,
        })
      }
      else {
        return 返回状态码及信息(500, null, 错误信息[numErrorCode] || error_msg)
      }
      // return 返回状态码及信息(200, apiRes.data)
    }
    catch (error) {
      console.error('error:', error)
      return 返回状态码及信息(500)
    }
  }
  else {
    return 返回状态码及信息(403)
  }
}
