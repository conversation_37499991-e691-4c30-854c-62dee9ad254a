// 中文字体
// (?<=src:\s*url\()['"]?([^()'"]+)['"]?(?=\))

// iconfont字体图标, 因为不会用正常方式使用iconfont, 所以其他的类都不需要, 把字体引入就行
@font-face {
  font-family: iconfont;
  src: url('@/assets/font/iconfont.woff2') format('woff2');
}

body, :root, .guide_wrapper {
  background-color: #fff;
}

body, :root, .guide_wrapper, .colorful_btn_text, .arco-textarea-word-limit {
  font-family: var(--main-font-family) !important;
}

.dark {
  color-scheme: dark;
}

::selection {
  @apply bg-primary text-#fff dark:bg-#646cff;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  border-width: 0;

  @apply bg-#c9cdd4 dark:bg-#5f5f60;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-#86909c dark:bg-#929293;
}

::-webkit-scrollbar-track-piece {
  @apply bg-#fff dark:bg-#272728;
}

::-webkit-scrollbar-corner {
  @apply bg-transparent;
}

button {
  -webkit-tap-highlight-color: transparent;
}

.code_font {
  font-family: var(--code-font-family);
}

.jp_font {
  font-family: var(--jp-font);
}
