import { pickBy } from 'lodash-es'

// const baseUrl = import.meta.env.VITE_UNIAPI_BASEURL

const { VITE_TTS_BASEURL: baseUrl } = import.meta.env

interface 朗读参数 {
  // 要朗读的文本
  text: string
  // 朗读文本的角色信息 不传默认zh-CN-YunjianNeural
  voice: string
  // 语速 0.5 ~ 3.0 不传默认1
  rate?: number
  // 语调 0 ~ 2 不传默认1
  pitch?: number
  express?: string // 发音风格
}

function 获取SSML以及音频格式参数信息(朗读参数: 朗读参数) {
  const { voice, rate = 0, pitch = 0, text } = 朗读参数
  const Format = 'audio-24khz-48kbitrate-mono-mp3'
  const SSML = `
    <speak xmlns="http://www.w3.org/2001/10/synthesis" xmlns:mstts="http://www.w3.org/2001/mstts" xmlns:emo="http://www.w3.org/2009/10/emotionml" version="1.0" xml:lang="en-US">
      <voice name="${voice}">
        <prosody  rate="${rate}%" pitch="${pitch}%">${text}</prosody >
      </voice >
      </speak >`

  return {
    Format,
    SSML,
  }
}

/**
 * 语音朗读 直接生成base64
 * @param 朗读参数
 * @returns
 */
export async function 语音朗读生成base64(朗读参数: 朗读参数) {
  const { Format, SSML } = 获取SSML以及音频格式参数信息(朗读参数)
  const apiRes = await fetch(`${baseUrl}/api/ra`, {
    method: 'post',
    headers: {
      Format,
    },
    body: SSML,
  }).then(res => res.blob())
  return apiRes
}

/**
 *  直接生成ArrayBuffer
 * @param 朗读参数
 * @returns
 */
export async function 语音朗读生成ArrayBuffer(朗读参数: 朗读参数) {
  if (window.servers) {
    const { Format, SSML } = 获取SSML以及音频格式参数信息(朗读参数)
    return window.servers.voiceReading(Format, SSML)
  }
}
