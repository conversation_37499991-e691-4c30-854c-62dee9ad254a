import { pickBy } from 'lodash-es'

// const baseUrl = import.meta.env.VITE_UNIAPI_BASEURL

const { VITE_TTS_BASEURL: baseUrl } = import.meta.env

interface 朗读参数 {
  // 要朗读的文本
  text: string
  // 朗读文本的角色信息 不传默认zh-CN-YunjianNeural
  voice: string
  // 语速 0.5 ~ 3.0 不传默认1
  rate?: number
  // 语调 0 ~ 2 不传默认1
  pitch?: number
  express?: string // 发音风格
}

/**
 * 将rate参数转换为新API需要的speed格式
 * 原来的rate是百分比形式，新API的speed是倍数形式
 */
function 转换语速参数(rate: number = 0): number {
  // rate为0表示正常语速，对应speed为1.0
  // rate为正数表示加快，rate为负数表示减慢
  // rate范围大概是-50到50，对应speed范围0.5到2.0
  const speed = 1.0 + (rate / 100)
  return Math.max(0.5, Math.min(2.0, speed))
}

/**
 * 将pitch参数转换为新API需要的格式
 * 原来的pitch是百分比形式，新API的pitch是字符串形式
 */
function 转换音调参数(pitch: number = 0): string {
  // 确保pitch在合理范围内 (-50 到 50)
  const clampedPitch = Math.max(-50, Math.min(50, pitch))
  return clampedPitch.toString()
}

/**
 * 语音朗读 直接生成base64
 * @param 朗读参数
 * @returns
 */
export async function 语音朗读生成base64(朗读参数: 朗读参数) {
  const { voice, rate = 0, pitch = 0, text, express = 'general' } = 朗读参数

  // 构建新API的请求参数
  const requestBody = {
    input: text,
    voice,
    speed: 转换语速参数(rate),
    pitch: 转换音调参数(pitch),
    style: express,
  }

  try {
    const apiRes = await fetch(`${baseUrl}/v1/audio/speech`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    if (!apiRes.ok) {
      throw new Error(`API请求失败: ${apiRes.status} ${apiRes.statusText}`)
    }

    const blob = await apiRes.blob()
    return blob
  }
  catch (error) {
    console.error('TTS API调用失败:', error)
    throw error
  }
}

/**
 * 直接生成ArrayBuffer
 * 现在统一使用HTTP API，不再区分环境
 * @param 朗读参数
 * @returns
 */
export async function 语音朗读生成ArrayBuffer(朗读参数: 朗读参数) {
  // 统一使用HTTP API，无论在什么环境中
  const blob = await 语音朗读生成base64(朗读参数)
  return blob.arrayBuffer()
}
