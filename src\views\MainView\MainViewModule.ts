// MainView用到的所有功能模块
import type { CascaderOption } from '@arco-design/web-vue'

export { 语种识别, 通用翻译 } from '@/apis/translation/index'
export { 用库推测语种, 用抽样推测语种 } from '@MainView/useCheckChinese'
export { 检查from和to是否兼容 } from '@MainView/useCheckFromTo'
export { 复制主函数 } from '@MainView/useCopy'
export * from '@MainView/useGuide'
export { use命名模式模块 } from '@MainView/useNamingMode'

export function 格式化级联显示内容(options: CascaderOption[]) {
  let 文字数组: string[] = []

  // 特殊情况：当第一个选项的value为'last'时，需要特殊处理第二个选项
  if (options.length > 1 && options?.[0]?.value === 'last') {
    // 第二个选项的label会形如"自动检测 > 中文-简"，需要分割成数组
    const secondLabel = options?.[1]?.label as string
    文字数组 = secondLabel.split(' > ')
  }
  else {
    // 常规情况：直接使用所有选项的label
    文字数组 = options.map(option => option.label as string)
  }

  return 文字数组.join('\u3000  \u3000')
}

export { useUtools } from '@MainView/useUtools'
