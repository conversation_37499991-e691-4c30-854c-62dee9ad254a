/**
 * 用户设置存储业务
 */
import { cloneDeep } from 'lodash-es'
import { 加密, 解密 } from '@/utils/rabbit'

// import { 用户设置存储 } from '@/store/userSetting' // 旧版要删
import { 用户配置存储 } from '@/store/useUserSetting'

export function 设置存储(formData: any) {
  // 从pinia读取设置
  // const settingStore = 用户设置存储() // 旧版要删
  const 用户配置 = 用户配置存储()

  // 获取设置并转换为表单格式
  function getSettingToFromData() {
    const { baseConfig } = 用户配置
    const keyConfig: any = 用户配置.keyConfig
    return {
      homeHasApi: baseConfig.homeOption, // 首页展示的翻译方式
      defaultApi: baseConfig.defaultApi, // 默认翻译方式
      textFont: baseConfig.fontSize, // 文本框字号
      copyBtnBehavior: baseConfig.copyBtnBehavior, // 复制快捷键行为 (open:仅复制|close:复制并隐藏|closeInput:复制隐藏并输入)
      copyBtnShow: baseConfig.copyBtnShow, // 首页显示复制按钮 []
      codeMode: baseConfig.codeMode, // 命名翻译模式
      codeModeOption: baseConfig.codeModeOption, // 命名翻译选项
      readAloud: baseConfig.readAloud, // 语音朗读
      readingPreference: baseConfig.readingPreference, // 朗读偏好
      theme: baseConfig.theme, // 主题
      defaultForeignLanguage: baseConfig.defaultForeignLanguage,
      readingModel: baseConfig.readingModel, // 语言模式
      removeN: baseConfig.removeN, // 去除换行符
      tripleBackspaceClear: baseConfig.tripleBackspaceClear, // 三次退格清空
      translationTriggerMode: baseConfig.translationTriggerMode, // 翻译触发方式
      lastDirectionStorageNum:baseConfig.lastDirectionStorageNum, // 最近翻译方向存储数量

      appid: keyConfig.baidu?.appid, // 百度
      token: keyConfig.baidu?.token, // 百度
      secretId: keyConfig.tencent?.secretId, // 腾讯
      secretKey: keyConfig.tencent?.secretKey, // 腾讯
      accessKeyId: keyConfig.ali?.accessKeyId, // 阿里
      accessKeySecret: keyConfig.ali?.accessKeySecret, // 阿里
      youdaoId: keyConfig.youdao?.appid, // 有道
      youdaoSecret: keyConfig.youdao?.appkey, // 有道
      caiyunToken: keyConfig.caiyun?.token, // 彩云
      huoshanAccessKeyId: keyConfig.huoshan?.accessKeyId, // 火山
      huoshanSecretAccessKey: keyConfig.huoshan?.secretAccessKey, // 火山
      xiaoniuApiKey: keyConfig.xiaoniu?.xiaoniuApiKey, // 小牛
      transmartToken: keyConfig.transmart?.token, // 腾讯交互
      transmartName: keyConfig.transmart?.user, // 腾讯交互
      deeplKey: keyConfig.deepl?.key, // DeepL密钥
      deeplType: keyConfig.deepl?.apiType || 'Free', // DeepL接口类型  Free|Pro|deeplx
      deeplxUrl: keyConfig.deepl?.deeplxUrl, // DeepLX请求地址
      deeplxToken: keyConfig.deepl?.deeplxToken, // DeepLX密钥
      googleType: keyConfig.google?.apiType || 'online',
    }
  }

  // 获取设置
  function 获取设置() {
    const tempFormData = getSettingToFromData()
    填写表单(tempFormData)
  }

  function 填写表单(value: any) {
    Object.assign(formData, value)
  }

  function 保存设置() {
    // 密钥格式转换
    const keyDatas = {
      baidu: {
        appid: formData.appid,
        token: formData.token,
      },

      tencent: {
        secretId: formData.secretId,
        secretKey: formData.secretKey,
      },

      youdao: {
        appid: formData.youdaoId,
        appkey: formData.youdaoSecret,
      },

      ali: {
        accessKeyId: formData.accessKeyId,
        accessKeySecret: formData.accessKeySecret,
      },

      caiyun: {
        token: formData.caiyunToken,
      },

      huoshan: {
        accessKeyId: formData.huoshanAccessKeyId,
        secretAccessKey: formData.huoshanSecretAccessKey,
      },

      xiaoniu: {
        xiaoniuApiKey: formData.xiaoniuApiKey,
      },

      transmart: {
        token: formData.transmartToken,
        user: formData.transmartName,
      },

      deepl: {
        key: formData.deeplKey,
        apiType: formData.deeplType,
        deeplxUrl: formData.deeplxUrl, // DeepLX请求地址
        deeplxToken: formData.deeplxToken, // DeepLX密钥
      },

      google: {
        apiType: formData.googleType, // 请求类型
      },
    }

    用户配置.saveBaseConfig({
      homeOption: formData.homeHasApi,
      defaultApi: formData.defaultApi,
      // keyConfig:keyDatas,
      fontSize: formData.textFont,
      copyBtnBehavior: formData.copyBtnBehavior,
      // codeMode,
      codeModeOption: formData.codeModeOption,
      copyBtnShow: formData.copyBtnShow,
      readAloud: formData.readAloud,
      readingPreference: formData.readingPreference,
      theme: formData.theme,
      defaultForeignLanguage: formData.defaultForeignLanguage,
      readingModel: formData.readingModel,
      removeN: formData.removeN,
      tripleBackspaceClear: formData.tripleBackspaceClear,
      translationTriggerMode: formData.translationTriggerMode,
      lastDirectionStorageNum:formData.lastDirectionStorageNum
    })
    用户配置.saveKeyConfig(keyDatas)
  }

  function 重置设置() {
    用户配置.reset()
  }

  function 导出设置(密码: string): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // const tempFormData = cloneDeep(settingStore.getSetingFormData)
        const tempFormData = cloneDeep(getSettingToFromData())
        const json = JSON.stringify(tempFormData)
        const 密文 = 加密(json, 密码)
        resolve(密文)
      }
      catch (error) {
        reject(error)
      }
    })
  }

  function 导入配置(文本: string, 密码: string) {
    return new Promise((resolve, reject) => {
      const json = 解密(文本, 密码)
      if (json) {
        try {
          const tempFormData = JSON.parse(json)
          填写表单(tempFormData)
          resolve(tempFormData)
        }
        catch (error) {
          reject(error)
        }
      }
      else {
        reject(new Error('期望转化结果为JSON'))
      }
    })
  }

  return {
    获取设置,
    保存设置,
    重置设置,
    导出设置,
    导入配置,
    getSettingToFromData,
  }
}
