<template>
  <div
    flex="~ col"
    border="~ #f2f3f4 dark:#444"
    class="setting_card rounded-md transition-cusbezier-300"
    shadow="[0_4px_8px_1px_#064bb30a] hover:[5px_5px_7px_#f7f7f7] dark:([9px_9px_18px_#232324] hover:[6px_6px_8px_#212121])"
  >
    <div
      p="y-4px x-18px"
      class="flex-y-c select-none overflow-hidden rounded-t-4px bg-primary/4 text-16px dark:(bg-#333)"
    >
      <slot name="title">
        {{ title }}
      </slot>
    </div>
    <div p="x-16px t-16px" flex="~ col 1" class="card_content overflow-y-auto">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
const { title = '' } = defineProps<{
  title?: string
}>()
</script>

<style lang="scss" scoped>
.card_content::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}
</style>
