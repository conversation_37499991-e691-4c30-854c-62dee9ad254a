// @unocss-include
interface 文案类型 {
  [key: string]: string
}

export const 文案映射: 文案类型 = {
  关于自动复制:
    'uTools在3.x版本移除了自动复制功能，后续新版本创建的全局快捷键将无法设置自动复制，但若快捷键是在2.x版本创建的，且创建时打开了自动复制开关，则在新版本依旧生效，你可以在网上寻找2.x版本的uTools，打开自动复制后再升级到后续版本',
  翻译服务: `
    <p>在首页您可以通过「Tab」键快速切换翻译服务, 最多选择7个翻译服务</p>
  `,
  主页显示顺序: '将根据「翻译服务」的勾选顺序进行排序',
  文本框字号: '可以输入14到20之间的数字, 控制翻译界面两个文本框的字体大小',
  翻译触发方式: `
    <p class="mt-6px text-[var(--color-text-1)] text-16px">输入文字时：</p>
    <ul class="list-disc pl-16px">
      <li>将在你输入文字时, 实时进行翻译, 体验丝滑无中断, 但会造成用量浪费, 适合经常复制粘贴的用户</li>
    </ul>
    <p class="mt-6px">比如“今天天气不错”这句话, 6个字可能消耗12个字的用量, 触发的翻译次数越多, 也将会造成越多的浪费</p>
    <p>如：</p>
    <ul class="list-disc pl-16px">
      <li>今天</li>
      <li>今天天气</li>
      <li>今天天气不错</li>
    </ul>
    <p class="mt-6px text-[var(--color-text-1)] text-16px">点击按钮/按下回车时：</p>
    <ul class="list-disc pl-16px">
      <li>不会造成字符的浪费, 输入完成后手动触发翻译, 适合经常手动输入文字的用户</li>
    </ul>
  `,
  快捷键行为: `
    <p>%s 可进行快捷键复制, 该选项可设置按下快捷键后会发生什么</p>
    <p class="mt-16px">如果插件处于分离状态, 则不会隐藏 / 隐藏并输入</p>
    `,
  底部显示的按钮: `
    <p>翻译页底部会显示的按钮, 请根据您的使用习惯勾选, 勾选多个将会在翻译完成后, 底部显示多个按钮, 以应对不同的情况</p>
    <p class="mt-16px">如果插件处于分离状态, 则不会隐藏 / 隐藏并输入</p>
    `,
  首选译文语言: `
    <ul class=" space-y-10px overflow-auto flex-1">
      <li>
        如果在翻译界面开启了
        <span class="text_important">「自动切换语种」</span>
        <div class="active_auto_btn_bg aspect-ratio-square w-20px flex-c inline-flex rounded-4px -v-1px">
          <span class="active_auto_btn">
        </div>
        </span>
        , 将在翻译之前判断输入是否为中文, 如果
        <span class="text_important">
          认定为中文
        </span>
        , 将会自动切换
        「目标语种」为一个外语, 该选项用来指定这个外语默认是什么, 如果
        <span class="text_important"> 不是中文 </span>
        , 则会切换为 “自动 → 中文” 尽可能免去你手动调整目标语种的这一步骤
      </li>
      <li>
        如果
        <span class="text_important">手动切换了</span>
        任一语种, 易翻会认为自己猜错了, 将自动关闭「自动切换语种」功能
        , 直至
        <span class="text_important">插件退出</span>
        , 但是你依然可以在语种下拉选择的左侧点击
        <div class="disabled_auto_btn_bg aspect-ratio-square w-20px flex-c inline-flex rounded-4px -v-1px">
          <span class="disabled_auto_btn">
        </div>
        按钮
        <span class="text_important"> 手动开启 </span>
      </li>
      <li>因为彩云小译只支持英、日、俄，所以只能开放这三个选项</li>
    </ul>
  `,
  去除译文换行: '开启后将尽可能去除翻译结果中的换行符',
  插件主题:
    '你可以通过在uTools的基本设置中, 将「主题」设置为「跟随系统」, 即可实现易翻跟随uTools, uTools跟随你的系统主题的效果',
  原文朗读: `
    <p>如果填写腾讯交互翻译的信息，识别语种会更加精准</p>
    <p class="mt-16px">
      虽然不填写也可以调用本地识别语种，但还是填写了识别的更精准。
    </p>
    <p class="mt-16px">
      如果识别的不正确, 你可以手动调整翻译的源语言进行更正, 故意乱选 (例如输入的是中文, 硬要选个西班牙语) 你将只能获得一个错误提示。
    </p>
    `,
  语音朗读: `
    <p>
      开启后, 文本框的左下角会出现语音朗读按钮, 点击按钮, 即可朗读翻译结果
    </p>
    <div class="mt-16px">
      <p>如果出现朗读失败, 可以用以下方式解决</p>
      <ul class="list-disc pl-16px">
        <li>再次点击按钮重试</li>
        <li>保证系统时间与北京时间相同</li>
        <li>减少需要朗读的文字</li>
      </ul>
    </div>
  `,
  朗读模式: `
  <div>
    <p class="mt-6px text-[var(--color-text-1)] text-16px">质量优先：</p>
    <ul class="list-disc pl-16px">
      <li>极具表现力, 拥有类似人类的声音</li>
      <li>支持易翻的<span class="text_important">所有语种</span></li>
      <li>朗读表现稳定</li>
      <li>需要网络连接</li>
    </ul>
    <p class="mt-6px text-[var(--color-text-1)] text-16px">速度优先：</p>
    <ul class="list-disc pl-16px">
      <li>比较生硬的机器声音</li>
      <li><span class="text_important">仅支持英文</span></li>
      <li>朗读表现视设备而定</li>
      <li>加载速度极快, 不需要网络</li>
    </ul>
  </div>
  `,
  朗读偏好: `系统默认是我们认为还不错的声音, 不同的语种下有男声也有女声, 你可以通过更改此选项, 来指定朗读声音的性别 <p class="text_important"> 速度优先模式下无效 </p>`,
  翻译服务数据: '你所申请翻译服务相关的数据, 应该填写在此处对应的地方',
  配置信息:
    '不是uTools会员？又想在多个设备上同步这些配置？导出会将配置信息写入你的剪贴板, 利用配置信息可以在其他设备进行导入。',
  三击退格清空输入:
    '开启后, 在输入框聚焦状态下, 连续敲击三次退格键「Backspace」, 将可以清空已经输入的文字',
  命名模式设置:
    '点击按钮进入命名模式设置页面, 可以自由启用/停用某个命名模式的关键字, 甚至是完全关闭某个命名模式',
  最近记录数: '在翻译方向语种下拉框中有个「最近」, 可以设置记录的个数',
}
