<template>
  <div
    ref="底栏Ref"
    text="20px #999"
    class="tools_bar_main size-full flex justify-center space-x-16px"
  >
    <CusTooltip content="图片翻译" :popup-container="底栏Ref">
      <SmoothTransitionIcon
        class="canActive"
        默认图标class="i-tabler-photo"
        hover时候的class="i-tabler-photo-filled"
        渲染标签="button"
        @click="节流函数(图片翻译弹窗启动)"
      />
    </CusTooltip>
    <CusTooltip
      :content="`去除译文换行：${去除译文换行 ? '开启' : '关闭'}`"
      :popup-container="底栏Ref"
    >
      <button
        class="canActive"
        :class="[
          去除译文换行
            ? 'text-primary i-fluent-text-wrap-24-filled'
            : 'i-fluent-text-wrap-off-24-filled',
        ]"
        @click="节流函数(换行开关)"
      />
    </CusTooltip>

    <CusTooltip
      :content="`译文编辑：${
        !命名模式 ? (结果框只读 ? '关闭' : '开启') : '命名模式下不可开启'
      }`"
      :popup-container="底栏Ref"
    >
      <button
        class="canActive"
        :class="[
          结果框只读
            ? 'i-fluent-edit-off-24-filled'
            : 'text-primary i-fluent-edit-24-filled',
          命名模式 && '!i-fluent-edit-prohibited-24-filled',
        ]"
        @click="节流函数(结果框控制按钮)"
      />
    </CusTooltip>

    <a-dropdown
      trigger="hover"
      :popup-container="底栏Ref"
      @select="切换主题($event as string)"
    >
      <button :class="主题图标类名" />
      <template #content>
        <a-dgroup title="主题">
          <a-doption v-for="item in 插件主题选项" :key="item.value" :value="item.value">
            <div class="drop_item" :class="{ 'text-primary': item.value === 主题 }">
              <span>{{ item.label }}</span>
              <span v-if="item.value === 主题" i-entypo-check />
            </div>
          </a-doption>
        </a-dgroup>
      </template>
    </a-dropdown>
    <a-dropdown
      v-if="语音朗读"
      trigger="hover"
      :popup-container="底栏Ref"
      @select="切换朗读模式($event as string)"
    >
      <button
        class="canActive i-akar-icons-sound-on text-primary"
        @click="节流函数(朗读开关, '关闭')"
      />
      <template #content>
        <a-dgroup title="朗读模式">
          <a-doption v-for="item in 朗读模式选项" :key="item.value" :value="item.value">
            <div class="drop_item" :class="{ 'text-primary': item.value === 朗读模式 }">
              <span>{{ item.label }}</span>
              <span v-if="item.value === 朗读模式" i-entypo-check />
            </div>
          </a-doption>
        </a-dgroup>
      </template>
    </a-dropdown>
    <CusTooltip
      v-else
      :content="`语音朗读：${语音朗读 ? '开启' : '关闭'}`"
      :popup-container="底栏Ref"
    >
      <button
        class="canActive i-akar-icons-sound-off"
        @click="节流函数(朗读开关, '开启')"
      />
    </CusTooltip>

    <a-dropdown
      trigger="hover"
      :popup-container="底栏Ref"
      @select="切换快捷键行为($event as string)"
    >
      <button i-ic-outline-keyboard-command-key />
      <template #content>
        <a-dgroup title="快捷键行为">
          <a-doption v-for="item in 快捷键行为选项" :key="item.value" :value="item.value">
            <div class="drop_item" :class="{ 'text-primary': item.value === 快捷键行为 }">
              <span>{{ item.label }}</span>
              <span v-if="item.value === 快捷键行为" i-entypo-check />
            </div>
          </a-doption>
        </a-dgroup>
      </template>
    </a-dropdown>

    <a-dropdown
      trigger="hover"
      :popup-container="底栏Ref"
      @select="切换触发方式($event as string)"
    >
      <button i-cus-yi class="scale-x-[0.83] scale-y-[0.88]" />
      <template #content>
        <a-dgroup title="翻译触发方式">
          <a-doption
            v-for="item in 翻译触发方式选项"
            :key="item.value"
            :value="item.value"
          >
            <div
              class="drop_item"
              :class="{ 'text-primary': item.value === 翻译触发方式 }"
            >
              <span>{{ item.label }}</span>
              <span v-if="item.value === 翻译触发方式" i-entypo-check />
            </div>
          </a-doption>
        </a-dgroup>
      </template>
    </a-dropdown>

    <template v-if="显示重置伸缩框按钮">
      <CusTooltip content="恢复下方文本框默认高度">
        <button
          class="canActive i-fluent-table-resize-row-16-regular"
          @click="恢复默认高度()"
        />
      </CusTooltip>
    </template>

    <CusTooltip content="如何申请翻译服务？">
      <button
        class="canActive i-fluent-question-circle-12-regular"
        @click="openUrl(utoolsConfig.docUrl)"
      />
    </CusTooltip>
  </div>
</template>

<script setup lang="ts">
import type { AnyFunction } from '@/types'
import {
  快捷键行为选项,
  插件主题选项,
  朗读模式选项,
  翻译触发方式选项,
} from '@/components/SettingModal/SettingsData'
import { utoolsConfig } from '@/config/utoolsConfig'
import { Message } from '@arco-design/web-vue'
import { 用户配置存储 } from '@MainView/MainViewData'
import { throttle } from 'lodash-es'

const { 命名模式, 显示重置伸缩框按钮 } = defineProps<{
  命名模式: boolean
  显示重置伸缩框按钮: boolean
}>()

const emits = defineEmits<{
  换行的值变了: [boolean]
  需要停止音频: []
  重置文本框高度: []
  需要focus: []
  打开图片翻译: []
}>()
const 结果框只读 = defineModel<boolean>('resultReadonly', { default: true })
const { baseConfig, saveBaseConfigByKey } = 用户配置存储()

const 底栏Ref = ref()
const 去除译文换行 = computed(() => baseConfig.removeN)
const 主题 = computed(() => baseConfig.theme)
const 朗读模式 = computed(() => baseConfig.readingModel)
const 快捷键行为 = computed(() => baseConfig.copyBtnBehavior)
const 语音朗读 = computed(() => baseConfig.readAloud)
const 主题图标类名 = computed(() => {
  const m = new Map([
    ['light', 'i-ph-sun-bold'],
    ['dark', 'i-ph-moon-bold'],
    ['auto', 'i-fluent-dark-theme-24-filled'],
  ])
  return m.get(主题.value) || 'i-fluent-dark-theme-24-filled'
})
const 翻译触发方式 = computed(() => baseConfig.translationTriggerMode)

const 节流函数 = throttle((fn: AnyFunction, ...args: any) => {
  fn(...args)
  emits('需要focus')
}, 300)

function 图片翻译弹窗启动() {
  emits('打开图片翻译')
}

function 切换朗读模式(模式: string) {
  emits('需要停止音频')
  emits('需要focus')
  saveBaseConfigByKey('readingModel', 模式)
}

function 切换主题(主题: string) {
  saveBaseConfigByKey('theme', 主题)
  emits('需要focus')
}

function 朗读开关(操作: '开启' | '关闭') {
  const 操作已开启 = !!(操作 === '开启')
  saveBaseConfigByKey('readAloud', 操作已开启)
  const msg = 操作已开启 ? '语音朗读开启' : '语音朗读关闭'
  const 类型 = 操作已开启 ? 'success' : 'error'
  switchMessage(类型, msg)
}

function 换行开关() {
  saveBaseConfigByKey('removeN', !去除译文换行.value)
  const msg = 去除译文换行.value ? '去除译文换行开启' : '去除译文换行关闭'
  const 类型 = 去除译文换行.value ? 'success' : 'error'
  switchMessage(类型, msg)
  emits('换行的值变了', 去除译文换行.value)
}

const 切换结果只读 = useToggle(结果框只读)
function 结果框控制按钮() {
  if (命名模式) {
    Message.warning('命名模式下不可开启')
    return
  }
  emits('需要停止音频')
  切换结果只读()
}

watch(结果框只读, (newVal) => {
  const msg = newVal ? '下方译文已恢复只读' : '下方译文已可编辑'
  const 类型 = newVal ? 'error' : 'success'
  switchMessage(类型, msg)
})

function 切换快捷键行为(行为: string) {
  saveBaseConfigByKey('copyBtnBehavior', 行为)
  emits('需要focus')
}

function 切换触发方式(触发方式: string) {
  Message.success('触发方式已切换')
  saveBaseConfigByKey('translationTriggerMode', 触发方式)
}

function 恢复默认高度() {
  emits('重置文本框高度')
  emits('需要focus')
}
</script>

<style lang="scss" scoped>
.tools_bar_main {
  button {
    @apply select-none transition-cusbezier-150 [&.canActive:active]:(scale-90);
  }
}

.drop_item {
  @apply flex-y-c space-x-4px;
}
</style>
