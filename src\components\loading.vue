<template>
  <div
    bg="#fff dark:#272728"
    flex="c col"
    class="size-full dark:(border-transparent)"
  >
    <LoadingIcon05 />
    <div class="mt-16px">
      <p
        text="16px [var(--color-text-3)]"
        class="text-center leading-normal"
      >
        正在翻译<span class="loading-dots inline-block text-left" />
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Loading',
})
</script>

<style lang="scss" scoped>
.loading-dots::after {
  content: '';
  animation: dots 6s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}
</style>
