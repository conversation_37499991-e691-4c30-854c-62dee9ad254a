import type { 语种 } from '@/assets/translateApiOption'

interface 角色信息 {
  name: string
  ShortName: string
  express?: string
}
const 角色列表: 角色信息[] = [
  { name: '晓晓', ShortName: 'zh-CN-XiaoxiaoNeural' },
  { name: '云健', ShortName: 'zh-CN-YunjianNeural' },
  { name: '云龙', ShortName: 'zh-HK-WanLungNeural' },
  { name: '晓佳', ShortName: 'zh-HK-HiuGaaiNeural' },
  { name: '<PERSON>', ShortName: 'en-US-JennyNeural' },
  { name: '<PERSON>', ShortName: 'en-US-GuyNeural' },
  { name: '圭太', ShortName: 'ja-JP-KeitaNeural' },
  { name: '七海', ShortName: 'ja-JP-NanamiNeural' },
  { name: '<PERSON><PERSON><PERSON>', ShortName: 'ru-RU-SvetlanaNeural' },
  { name: '<PERSON>', ShortName: 'ru-RU-<PERSON>Neural' },
  { name: 'in<PERSON><PERSON>', ShortName: 'ko-KR-InJoonNeural' },
  { name: '<PERSON>-<PERSON>', ShortName: 'ko-KR-SunHiNeural' },
  { name: '<PERSON>', ShortName: 'de-<PERSON>-<PERSON>eural' },
  { name: '<PERSON>ke', <PERSON><PERSON>ame: 'de-DE-Elke<PERSON>eural' },
  { name: '<PERSON>', Short<PERSON>ame: 'fr-FR-<PERSON>eural' },
  { name: '<PERSON>', Short<PERSON>ame: 'fr-FR-<PERSON><PERSON>eural' },
  { name: 'Alvaro', Short<PERSON>ame: 'es-ES-Alvaro<PERSON>eural' },
  { name: 'Elvira', Short<PERSON>ame: 'es-ES-ElviraNeural' },
  { name: 'Achara', ShortName: 'th-TH-AcharaNeural' },
  { name: 'Niwat', ShortName: 'th-TH-NiwatNeural' },
  { name: 'Duarte', ShortName: 'pt-PT-DuarteNeural' },
  { name: 'Fernanda', ShortName: 'pt-PT-FernandaNeural' },
  { name: 'Isabella', ShortName: 'it-IT-IsabellaNeural' },
  { name: 'Diego', ShortName: 'it-IT-DiegoNeural' },
]

function 获取发音人信息(str = '') {
  return 角色列表.find(item => item.name === str) ?? 角色列表[0]
}

interface 声音映射item {
  male: 角色信息
  female: 角色信息
  default: 角色信息
  rate?: number
}

export type 声音映射Type = {
  [key in 语种]?: 声音映射item
}

export const 声音映射: 声音映射Type = {
  zh: {
    male: 获取发音人信息('云健'),
    female: 获取发音人信息('晓晓'),
    default: 获取发音人信息('晓晓'),
  },
  en: {
    male: 获取发音人信息('Guy'),
    female: 获取发音人信息('Jenny'),
    default: 获取发音人信息('Jenny'),
  },
  jp: {
    male: 获取发音人信息('圭太'),
    female: 获取发音人信息('七海'),
    default: 获取发音人信息('圭太'),
  },
  ru: {
    male: 获取发音人信息('Dmitry'),
    female: 获取发音人信息('Svetlana'),
    default: 获取发音人信息('Svetlana'),
  },
  de: {
    male: 获取发音人信息('Conrad'),
    female: 获取发音人信息('Elke'),
    default: 获取发音人信息('Conrad'),
  },
  fra: {
    male: 获取发音人信息('Henri'),
    female: 获取发音人信息('Denise'),
    default: 获取发音人信息('Henri'),
  },
  spa: {
    male: 获取发音人信息('Alvaro'),
    female: 获取发音人信息('Elvira'),
    default: 获取发音人信息('Elvira'),
  },
  it: {
    male: 获取发音人信息('Diego'),
    female: 获取发音人信息('Isabella'),
    default: 获取发音人信息('Isabella'),
  },
  pt: {
    male: 获取发音人信息('Duarte'),
    female: 获取发音人信息('Fernanda'),
    default: 获取发音人信息('Fernanda'),
  },
  th: {
    male: 获取发音人信息('Niwat'),
    female: 获取发音人信息('Achara'),
    default: 获取发音人信息('Niwat'),
  },
  yue: {
    male: 获取发音人信息('云龙'),
    female: 获取发音人信息('晓佳'),
    default: 获取发音人信息('云龙'),
    rate: 1.1,
  },
  cht: {
    male: 获取发音人信息('云健'),
    female: 获取发音人信息('晓晓'),
    default: 获取发音人信息('云健'),
  },
  kor: {
    male: 获取发音人信息('inJoon'),
    female: 获取发音人信息('Sun-Hi'),
    default: 获取发音人信息('inJoon'),
  },
}
