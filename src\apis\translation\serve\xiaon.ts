/**
 *  小牛翻译接口
 * https://niutrans.com/documents/contents/trans_text#accessMode
 */

import axios from 'axios'

import { type 翻译参数Type, 返回状态码及信息 } from '../common'

const 错误信息: Record<string, string> = {
  0: '请求参数有误, 请检查参数',
  1: 'Content-Type不支持【multipart/form-data】',
  10000: '输入为空',
  10001: '请求频繁, 超出QPS限制',
  10003: '请求字符串长度超过限制',
  10005: '源语编码有问题, 非UTF-8',
  13001: '字符流量不足或者没有访问权限',
  13002: '"apikey"参数不可以是空',
  13003: '内容过滤异常',
  13007: '语言不支持',
  13008: '请求处理超时',
  14001: '分句异常',
  14002: '分词异常',
  14003: '后处理异常',
  14004: '对齐失败, 不能够返回正确的对应关系',
}

const last = {
  optionsStr: '',
  result: '',
}

/**
 * 通用翻译
 * @param {string} options.q 请求翻译query(UTF-8编码)
 * @param {string} options.from 翻译源语言(可设置为auto)
 * @param {string} options.to 翻译目标语言 (可设置为auto)
 * @param {object} options.keyConfig key配置
 */
export function textTranslation({ q, from, to, keyConfig }: 翻译参数Type) {
  const url = import.meta.env.VITE_XIAONIU_BASEURL
  const { xiaoniuApiKey } = keyConfig

  const params = {
    from,
    to,
    src_text: q,
    apikey: xiaoniuApiKey,
  }

  return axios
    .post(url, params)
    .then((res) => {
      const { error_code, tgt_text } = res.data
      const numErrorCode = Number.parseInt(error_code, 10)

      if (numErrorCode) {
        if (numErrorCode === 411) {
          last.result = 返回状态码及信息(503)
          return last.result
        }
        last.result = 返回状态码及信息(500, null, 错误信息[numErrorCode] || '未知错误')
        return last.result
      }

      // 翻译成功
      last.result = 返回状态码及信息(200, { text: tgt_text })
      return last.result
    })
    .catch((err) => {
      return 返回状态码及信息(500, null, err)
    })
}
