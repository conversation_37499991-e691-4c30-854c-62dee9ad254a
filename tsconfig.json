{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"], "@MainView/*": ["src/views/MainView/*"]}, "resolveJsonModule": true, "types": ["vite/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "outDir": "./", "removeComments": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules", "public"], "extensions": [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json"]}