/** utools 工具 */
import axios from 'axios'
import dayjs from 'dayjs'
import hmacSHA256 from 'crypto-js/hmac-sha256'
import { utoolsConfig } from '@/config/utoolsConfig'

const baseUrl = 'https://open.u-tools.cn'

const utools = window?.utools
export function 获取插件应用动态创建的功能(codes?: string[]) {
  if (!utools) {
    return
  }
  return utools.getFeatures(codes)
}

export function 为本插件应用动态新增某个功能(keyConfig: any) {
  const params = JSON.parse(JSON.stringify(keyConfig))
  if (!utools) {
    return
  }

  try {
    utools.setFeature(params)
  }
  catch (error) {
    console.error('为本插件应用动态新增某个功能异常:', {
      keyConfig,
      error,
    })
  }
}

export function 动态删除本插件应用的某个功能(code: string) {
  if (!utools) {
    return
  }
  try {
    code && utools.removeFeature(code)
  }
  catch (error) {
    console.error('动态删除本插件应用的某个功能异常:', {
      code,
      error,
    })
  }
}

// 获取用户服务端临时令牌, 2 小时内有效
async function 获取临时token() {
  const res = await utools.fetchUserServerTemporaryToken()
  return res.token
}

/** 获取签名 */
function getSign(params: any) {
  // 1. 对参数按照键名进行升序排序
  const sortedKeys = Object.keys(params).sort()
  const sortedParams: any = {}
  sortedKeys.forEach((key) => {
    sortedParams[key] = params[key]
  })

  // 2. 生成 URL-encode 之后的请求字符串
  const str = new URLSearchParams(sortedParams).toString()

  // 3. 使用 HMAC 方法生成带有密钥的哈希值
  const secret = utoolsConfig.secret
  // const sign = CryptoJS.HmacSHA256(str, secret).toString()
  const sign = hmacSHA256(str, secret).toString()
  return sign
}

// 获取当前用户, 未登录帐号返回 null
export async function 获取用户信息() {
  // const data = utools ? utools.getUser() : null
  if (!utools) {
    // return null
    return {
      avatar: '',
      nickname: '浏览器环境',
      type: '-',
      open_id: '-',
      member: '-',
    }
  }

  try {
    const plugin_id = utoolsConfig.plugin_id
    const access_token = await 获取临时token()
    const timestamp = dayjs().unix().toString()
    const sign = getSign({
      plugin_id,
      access_token,
      timestamp,
    })
    const params = {
      plugin_id,
      access_token,
      timestamp,
      sign,
    }

    const apiRes = (await axios.get(`${baseUrl}/baseinfo`, { params })) as any
    // const { data } = apiRes?.resource
    return apiRes.data.resource
  }
  catch (error) {
    return {
      avatar: '',
      nickname: '未登录用户',
      type: '-',
      open_id: '-',
      member: '-',
    }
  }
}

/** 切换至主窗口 */
export function showMainWindow() {
  // 执行该方法将会显示 uTools 主窗口, 包括此时正在主窗口运行的插件
  utools && utools.showMainWindow()
}
