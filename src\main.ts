import { createApp } from 'vue'
import '@unocss/reset/tailwind.css'
import { createPinia } from 'pinia'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import App from './App.vue'
import '@arco-design/web-vue/dist/arco.less'
import '@arco-design/web-vue/es/message/style/css.js'
import 'virtual:uno.css'
import '@/styles/index.scss'
import 'shepherd.js/dist/css/shepherd.css'
import '@fontsource/inter/500.css'
import '@fontsource/jetbrains-mono/500.css'

dayjs.extend(utc)
createApp(App).use(createPinia()).mount('#app')
