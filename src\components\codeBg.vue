<template>
  <div position="absolute inset-0" class="code_bg grid-c select-none">
    <svg class="w-full">
      <text x="50%" y="70%" text-anchor="middle">{{ text }}</text>
    </svg>
  </div>
</template>

<script setup lang="ts">
defineProps({
  text: {
    type: String,
    default: '<code/>',
  },
})
</script>

<style lang="scss" scoped>
.code_bg {
  text {
    animation: code-bg-animated 2.5s var(--ani-bezier);
    animation-fill-mode: both;
    font-size: 100px;
    font-family: var(--code-font-family);
  }
}
</style>
