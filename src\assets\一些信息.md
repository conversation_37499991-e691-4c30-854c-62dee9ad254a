### 谷歌服务器出现问题的解决方式
先重启服务器
```bash
sudo service docker start && cd /home/<USER>/translateer/ && docker-compose restart
```

### 谷歌接口黑名单
```javascript
const arr1 = ['*************', '***********', '**************', '*************', '*************', '*************', '***************', '**************', '**************', '************', '************', '**************', '**************', '***********', '**************', '************', '************', '*************', '***************', '*************', '************', '************', '*************', '**************', '*************', '**************', '*************', '***************', '***************', '**************', '***************', '************', '************', '*************', '*************', '**************', '**************', '*************', '**************', '************', '***************', '**************', '**************', '**************', '***************', '*************', '*************', '***************', '**************', '**************', '**************', '**************', '*************', '*************', '*************', '*************', '118.112.57.107', '118.251.170.32', '119.123.131.33', '119.131.146.232', '119.145.98.197', '119.246.213.33', '119.39.76.151', '119.53.26.224', '119.78.254.2', '120.199.32.90', '120.230.122.124', '120.234.28.58', '121.10.41.145', '121.13.152.251', '121.193.129.128', '121.225.130.221', '121.225.80.177', '121.237.46.82', '121.31.20.76', '122.238.45.251', '122.96.34.236', '122.97.222.137', '123.138.70.76', '123.139.58.98', '123.174.41.87', '124.128.31.90', '124.240.87.234', '124.64.237.38', '124.64.239.157', '124.77.84.46', '124.89.117.2', '125.70.77.15', '139.227.167.96', '139.227.209.131', '14.109.209.159', '14.146.94.181', '14.150.107.142', '14.221.239.162', '140.99.151.13', '140.99.157.234', '170.178.173.10', '171.12.11.106', '171.8.225.229', '175.0.61.106', '175.171.183.203', '18.182.37.162', '180.124.224.170', '180.143.218.92', '180.162.106.204', '180.164.68.182', '182.118.239.6', '182.139.216.66', '182.90.207.26', '183.131.243.202', '183.197.178.44', '183.247.191.3', '183.251.161.69', '183.46.251.223', '185.159.68.96', '197.255.192.74', '202.100.231.38', '205.198.120.53', '210.13.80.220', '210.13.80.221', '210.27.197.13', '211.136.245.50', '211.142.247.137', '211.148.99.174', '217.115.75.200', '218.107.32.11', '218.16.69.117', '218.77.82.140', '219.134.231.206', '219.147.57.35', '219.217.153.222', '219.227.68.137', '220.136.107.252', '220.160.201.98', '220.202.217.98', '221.126.253.226', '221.192.179.233', '221.213.101.20', '222.128.55.82', '222.172.157.236', '222.178.202.65', '222.209.33.212', '222.244.81.52', '222.64.106.9', '222.78.209.113', '222.85.110.71', '222.94.229.254', '223.104.38.212', '223.68.32.168', '223.89.237.151', '223.94.220.101', '27.201.112.134', '27.39.221.245', '36.112.29.20', '36.142.184.170', '*************', '************', '**************', '*************', '*************', '*************', '**************', '*************', '*************', '*************', '*************', '************', '************', '**************', '*************', '*************', '*************', '************', '*************', '************', '***********', '*************', '**************', '************', '***********', '************', '************', '*************']

function mergeAndDeduplicateIPs(ipArray, ...userInfoArrays) {
  const 白名单 = ['**************', '**************', '**************', '***************', '************']
  // 从 userInfoArrays 中提取 IP 地址
  const 接口中的ipArr = userInfoArrays.flat().map(userInfo => userInfo.ip)
  // 合并 ipArray 和 extractedIPs
  const 已有ip合并接口ipArr = [...ipArray, ...接口中的ipArr]
  return [...new Set(已有ip合并接口ipArr)].filter(ip => !白名单.includes(ip)).sort()
}
console.log(JSON.stringify(mergeAndDeduplicateIPs(arr1, arr2), null, 0))
```
