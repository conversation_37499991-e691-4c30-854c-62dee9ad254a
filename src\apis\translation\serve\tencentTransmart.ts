/**
 * 腾讯交互
 * https://transmart.qq.com/zh-CN/index
 */
import axios from 'axios'
import { type 翻译参数Type, 读取并检查密钥配置, 返回状态码及信息 } from '../common'
import { 本地判断语种 } from '@/utils/languageAnalysis'
import { 获取推断语言名称 } from '@/utils/language'

/** 语言识别 */
export async function textAanalysis(text: string) {
  const url = `${import.meta.env.VITE_TENCENT_TRANSMART_BASEURL}`
  let token = ''; let user = ''
  const { flag, keyConfig } = 读取并检查密钥配置('transmart')
  if (flag) {
    token = keyConfig?.token
    user = keyConfig?.user

    const datas = {
      header: {
        fn: 'text_analysis',
        token,
        user,
      },
      text,
      type: 'plain',
    }

    const tencentRes = await axios.post(url, datas)
    const tencentResLang = tencentRes.data?.language
    if (tencentResLang) {
      return tencentResLang
    }
    else {
      const localResutl = await 本地判断语种(text)
      return localResutl
    }
  }
  else {
    const lang = await 本地判断语种(text)
    return lang
  }
}

/** 获取推断语言名称  */
function getTheLanguageName(fromTag: string) {
  const from = 获取推断语言名称('transmart', fromTag)
  return from
}

/**
 * 腾讯交互
 * @param {string} options.q 请求翻译query
 * @param {string} options.from 翻译源语言(可设置为auto)
 * @param {string} options.to 翻译目标语言(不可设置为auto)
 * @param {object} options.keyConfig key配置
 */
export async function textTranslation({ q, from, to, keyConfig }: 翻译参数Type) {
  const url = import.meta.env.VITE_TENCENT_TRANSMART_BASEURL
  const { token, user } = keyConfig

  let sourceTag = from
  if (sourceTag === 'auto') {
    sourceTag = await textAanalysis(q!)
  }

  if (sourceTag === to) {
    const from = getTheLanguageName(sourceTag)
    return 返回状态码及信息(200, { q, from })
  }

  const datas = {
    header: {
      fn: 'auto_translation_block',
      token,
      user,
    },
    type: 'plain',
    model_category: 'normal',
    source: {
      lang: sourceTag,
      text_block: q,
    },
    target: {
      lang: to,
    },
  }

  try {
    const apiRes = await axios.post(url, datas)
    const { header, auto_translation, message } = apiRes.data
    if (header.ret_code === 'succ') {
      // 成功
      const text = auto_translation
      const from = getTheLanguageName(sourceTag)
      return 返回状态码及信息(200, { text, from })
    }
    else {
      return 返回状态码及信息(500, null, message)
    }
  }
  catch (error: any) {
    return 返回状态码及信息(500, null, error)
  }
}
