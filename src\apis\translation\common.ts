import { textTranslation as 百度翻译 } from './serve/baidu'
import { textTranslation as 腾讯翻译 } from './serve/tencent'
import { textTranslation as 谷歌翻译 } from './serve/google'
import { textTranslation as 有道翻译 } from './serve/youdao'
import { textTranslation as 阿里翻译 } from './serve/ali'
import { textTranslation as 彩云翻译 } from './serve/caiyun'
import { textTranslation as 火山翻译 } from './serve/huoshan'
import { textTranslation as 小牛翻译 } from './serve/xiaon'
import { textTranslation as 腾讯交互翻译 } from './serve/tencentTransmart'
import { textTranslation as DeepL } from './serve/deepl'
import { imgTranslation as 百度图片翻译 } from './serve/baidu-img'
import { 用户配置存储 } from '@/store/useUserSetting'
import type { AnyFunction } from '@/types'

interface 翻译应用Type {
  [key: string]: {
    fn: AnyFunction
    label: string
    keys: string[]
  }
}

export interface 翻译参数Type {
  q?: string // 请求翻译query(UTF-8编码)
  from: string // 翻译源语言(可设置为auto)
  to: string // 翻译目标语言 (不可设置为auto)
  keyConfig?: any
  file?: Blob
}

const 翻译应用: 翻译应用Type = {
  baidu: {
    fn: 百度翻译,
    label: '百度翻译',
    keys: ['appid', 'token'],
  },
  tencent: {
    fn: 腾讯翻译,
    label: '腾讯翻译',
    keys: ['secretId', 'secretKey'],
  },
  google: {
    fn: 谷歌翻译,
    label: '谷歌翻译',
    keys: [],
  },
  youdao: {
    fn: 有道翻译,
    label: '有道翻译',
    keys: ['appid', 'appkey'],
  },
  ali: {
    fn: 阿里翻译,
    label: '阿里翻译',
    keys: ['accessKeyId', 'accessKeySecret'],
  },
  caiyun: {
    fn: 彩云翻译,
    label: '彩云小译',
    keys: ['token'],
  },
  huoshan: {
    fn: 火山翻译,
    label: '火山翻译',
    keys: ['accessKeyId', 'secretAccessKey'],
  },
  xiaoniu: {
    fn: 小牛翻译,
    label: '小牛翻译',
    keys: ['xiaoniuApiKey'],
  },
  transmart: {
    fn: 腾讯交互翻译,
    label: '腾讯交互翻译',
    keys: ['token', 'user'],
  },
  deepl: {
    fn: DeepL,
    label: 'DeepL',
    keys: ['key', 'apiType'],
  },
  baiduImg: {
    fn: 百度图片翻译,
    label: '百度图片翻译',
    keys: ['appid', 'token'],
  },
}

/** 根据tag获取翻译方法 */
export function 获取指定的翻译方法(tag: string) {
  return 翻译应用?.[tag]?.fn
}

/**
 * 返回信息对象
 * @param code 状态码
 * @param data 翻译返回值信息
 * @param customMsg 自定义信息值
 * @returns 组装后的响应对象
 */
export function 返回状态码及信息(code: number, data: any = {}, customMsg: string = '') {
  const codeOptions: { [key: number]: string } = {
    200: '成功',
    304: '成功(上次返回结果)',
    503: '操作太快了, 点击重试再试一次吧',
    500: '出现了一些问题, 如果你确定服务信息填写无误, 点击重试再试一次吧',
    501: '该服务正在维护中，请先使用其他服务',
    400: '参数信息不正确',
    401: '未配置密钥或密钥信息不完整',
    403: '请使用uTools来调用该接口',
    204: '请求已取消',
  }

  let message = codeOptions[code] || '未知错误'
  data = data || {}

  if (![200, 304].includes(code)) {
    message = `翻译失败：${customMsg || message} ${getRandomEmoticon()}`
    data.text = message || '1'
    if (code === 204) {
      data.text = ''
    }
  }

  return { code, message, ...data }
}
interface 检查结果Type {
  flag: boolean
  keyConfig: any
  msg: string
}

/**
 * 获取并检查密钥配置
 * @param tag 配置标签
 * @returns 检查结果
 */
export function 读取并检查密钥配置(tag: string): 检查结果Type {
  const serve = 翻译应用[tag]
  const { getKeyByTag } = 用户配置存储()

  const keyConfig = getKeyByTag(tag === 'baiduImg' ? 'baidu' : tag)

  let isMissingKey
  // 当使用DeepL且用的是DeepLX时，通过判断deeplxUrl字段来判断是否有配置
  if (tag === 'deepl' && keyConfig.apiType === 'deeplx') {
    isMissingKey = !keyConfig.deeplxUrl
  }
  else {
    // 其他情况，按照原有逻辑检查
    isMissingKey = serve.keys?.some(keyItem => !keyConfig[keyItem])
  }

  const flag = !isMissingKey
  const msg = isMissingKey
    ? `没有配置服务哦🚨, 我猜你大概率是没有填${serve.label}的信息, 现在, 你应该马不停蹄的点击右下角的设置按钮, 去填写相关信息`
    : ''
  return { flag, keyConfig, msg }
}
