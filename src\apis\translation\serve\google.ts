/**
 * 谷歌翻译接口
 * https://github.com/vitalets/google-translate-api
 */
// import encHex from 'crypto-js/enc-hex'
// import md5 from 'crypto-js/md5'
import axios from 'axios'
import { type 翻译参数Type, 返回状态码及信息 } from '../common'
// import { 根据时间差计算网络时间 } from '@/apis/time'
// import { userStore } from '@/store/useUser'
import { 语音标识修正 } from '@/utils/language'
/**
 * 分割字符串，考虑所有类型的换行符。
 * @param {string} text - 需要分割的字符串。
 * @return {string[]} - 根据换行符分割的字符串数组。
 */
function splitByNewline(text: string) {
  // 使用正则表达式匹配 \r\n（Windows），\r（旧 Mac），\n（Unix/Linux）。
  // \r\n 必须首先匹配，避免将其错误地分为两个换行符。
  return text.split(/\r\n|\r|\n/)
}

/**
 * 机器翻译
 * @param {string} options.q 请求翻译query
 * @param {string} options.from 翻译源语言(可设置为auto)
 * @param {string} options.to 翻译目标语言(不可设置为auto)
 * @param {object} options.keyConfig key配置
 */
export async function textTranslation({ q, from, to, keyConfig }: 翻译参数Type) {
  const apiType = keyConfig.apiType || 'online'
  // const user = userStore()

  // const apiRes = await window.servers.googleTextTranslateByLocal()
  // return 返回状态码及信息(501)

  // if (apiType === 'local') {
  //   try {
  //     const apiRes = await window.servers.googleTextTranslateByLocal({
  //       text: q,
  //       from,
  //       to,
  //     })
  //     // 返回状态码及信息(200, { ...apiRes, code: 200 })
  //     return 返回状态码及信息(200, { text: apiRes.result })
  //   }
  //   catch (error: any) {
  //     return 返回状态码及信息(500, null, error.toString())
  //   }
  // }
  // else
  if (apiType === 'online' || 1) {
    // const baseUrl = import.meta.env.VITE_SERVER
    // const action = import.meta.env.VITE_G_ACTION

    // const url = `${baseUrl}${action}`
    const version = 'v' + '2'
    // if (version === 'v1') {
    //   // V1版本： 通过签名调用服务版本
    //   try {
    //   // 签名
    //     const appid = import.meta.env.VITE_G_ID
    //     const { userInfo, gKey } = user
    //     // const appkey = user.gKey
    //     const { sign, salt, curtime } = toSign(appid, gKey, q!)

    //     const submitUserInfo = () => {
    //       if (userInfo.open_id) {
    //         return {
    //           ...userInfo,
    //           openId: userInfo.open_id,
    //         }
    //       }
    //       else {
    //         return {
    //           avatar: '',
    //           member: '',
    //           nickname: '',
    //           openId: '',
    //           timestamp: '',
    //           type: '',
    //         }
    //       }
    //     }
    //     const temp = 语音标识修正('google', { from, to })
    //     const params = {
    //       text: q,
    //       from: temp.from,
    //       to: temp.to,
    //       sign,
    //       sata: salt,
    //       time: curtime,
    //       user: submitUserInfo(),
    //     }

    //     // const apiRes = await axios.post(url, params)
    //     const headers = {
    //       'Content-Type': 'application/json;chrset=utf-8',
    //     }

    //     const apiRes = await window.servers.request(url, {
    //       method: 'POST',
    //       data: JSON.stringify(params),
    //       headers,
    //     })

    //     const { code = 500, data, msg = '未知错误' } = apiRes

    //     if (code === 200) {
    //     // 翻译成功
    //       return 返回状态码及信息(200, { text: data.result })
    //     }

    //     // 翻译失败
    //     return 返回状态码及信息(500, null, msg)
    //   }
    //   catch (error: any) {
    //     return 返回状态码及信息(500, null, error)
    //   }
    // }
    if (version === 'v2') {
      // V2版本：直接调用随机api方法返回
      // 使用splitByNewline函数分割输入文本
      const qArr = splitByNewline(q || '').filter(item => item !== '')
      // 使用Promise.all并发处理翻译请求
      const translations = await Promise.all(qArr.map(part => translateTextV2(part, from, to)))

      // 拼接翻译结果并返回
      return 返回状态码及信息(200, { text: translations.join('\n\n') })

      // translateTextV2函数用于处理单个文本片段的翻译
      async function translateTextV2(textPart: string, from: string, to: string) {
        const temp = 语音标识修正('google_2', { from, to })
        const cleanedText = textPart.replace(/\//g, '-')
        const apiUrl = `https://ef-google-cf.eeff.fun/api/v1/${temp.from}/${temp.to}/${encodeURIComponent(cleanedText)}`
        try {
          const apiRes = await axios(apiUrl, { method: 'GET' })
          const { translation } = apiRes.data
          return translation || ''
        }
        catch (error) {
          return ''
        }
      }
    }
    else if (version === 'v3') {
      const temp = 语音标识修正('google', { from, to })
      const qArr = splitByNewline(q!).filter(item => item !== '')
      const tranfromArr = await Promise.all(qArr.map(qLimit => tranfrom(qLimit)))

      return 返回状态码及信息(200, { text: tranfromArr.join('\n\n') })

      function tranfrom(qLimit: string) {
        return new Promise((resolve) => {
          const baseUrl = 'https://findmyip.net/api/translate.php'
          const url = `${baseUrl}?text=${encodeURIComponent(String(qLimit || ''))}&source_lang=${temp.from}&target_lang=${temp.to}`
          const headers = {
            'Content-Type': 'application/json;chrset=utf-8',
          }
          axios(url, {
            method: 'GET',
            headers,
          }).then((apiRes) => {
            // 翻译成功
            // return 返回状态码及信息(200, { text: data.result })
            const { code, data } = apiRes.data
            const { translate_result: translation } = data
            if (code === 200) {
              // return 返回状态码及信息(200, { text: translation })
              resolve(translation)
            }
            else {
              // return 返回状态码及信息(500, null, '服务错误')
              resolve('')
            }
          }).catch(() => {
            resolve('')
          })
        })
      }
    }
  }
}

// /**
//  * 根据查询字符串生成截断字符串
//  * @param query - 查询字符串
//  * @returns 截断后的字符串
//  */
// function truncate(query: string): string {
//   const length = query.length
//   if (length <= 18) {
//     return query
//   }
//   return query.substring(0, 13) + length + query.substring(length - 13)
// }

// /**
//  * 生成签名
//  * @param appid - 应用ID
//  * @param appkey - 应用密钥
//  * @param query - 查询字符串
//  * @returns 签名及相关信息
//  */
// function toSign(appid: string, appkey: string, query: string) {
//   const dateValue = 根据时间差计算网络时间()
//   const salt = dateValue.toString()
//   const curtime = Math.round(dateValue / 1000)
//   const str1 = appid + truncate(query) + salt + curtime + appkey
//   const sign = md5(str1).toString(encHex)
//   return { sign, salt, curtime }
// }
