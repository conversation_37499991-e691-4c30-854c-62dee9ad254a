<script setup lang="ts">
interface Props {
  value: string | number
}

interface GroupRef {
  modelValue: Ref<string | number>
  registerItem: (value: string | number, element: HTMLElement) => void
  unregisterItem: (value: string | number) => void
}

const props = defineProps<Props>()
const itemRef = ref<HTMLElement>()
const groupRef = inject<GroupRef>('segmentedGroupRef')

const isActive = computed(() => groupRef?.modelValue.value === props.value)

function handleSelect() {
  if (groupRef) {
    groupRef.modelValue.value = props.value
  }
}

onMounted(() => {
  if (itemRef.value && groupRef) {
    groupRef.registerItem(props.value, itemRef.value)
  }
})

onBeforeUnmount(() => {
  if (groupRef) {
    groupRef.unregisterItem(props.value)
  }
})
</script>

<template>
  <div
    ref="itemRef"
    class="segmented-item z-1 flex-c cursor-pointer px-12px transition-cusbezier-200"
    :class="{
      active: isActive,
    }"
    role="tab"
    :aria-selected="isActive"
    @click="handleSelect"
  >
    <slot />
  </div>
</template>

<style scoped lang="scss">
</style>
