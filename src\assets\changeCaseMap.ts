import {
  camelCase,
  capitalCase,
  constantCase,
  dotCase,
  kebabCase,
  noCase,
  pascalCase,
  pathCase,
  snakeCase,
  trainCase,
} from 'change-case'
import { titleCase } from 'title-case'

/** 中划线形式的常量 */
function constantLineCase(val: string) {
  return constantCase(val).replace(/_/g, '-')
}

export const 切换类型数组 = [
  {
    name: 'camelCase',
    handle: camelCase,
    label: '驼峰(小)',
    remark: '驼峰(小), 例如: userName',
    keyConfig: {
      label: '易翻翻译',
      code: 'codeMode__camelCase',
      explain: '命名模式：小驼峰；如: fooBar',
      cmds: ['mmxt(小驼峰)'],
      anyCmds: [
        {
          type: 'over',
          label: '小驼峰',
        },
      ],
    },
  },
  {
    name: 'pascalCase',
    handle: pascalCase,
    label: '驼峰(大)',
    remark: '驼峰(大)/帕斯卡, 例如: UserName',
    keyConfig: {
      label: '易翻翻译',
      code: 'codeMode__pascalCase',
      explain: '命名模式：大驼峰/帕斯卡；如: FooBar',
      cmds: ['mmdt(大驼峰)', 'mmpsk(帕斯卡)'],
      anyCmds: [
        {
          type: 'over',
          label: '大驼峰',
        },
      ],
    },
  },
  {
    name: 'snakeCase',
    handle: snakeCase,
    label: '下划线',
    remark: '下划线, 例如: user_name',
    keyConfig: {
      code: 'codeMode__snakeCase',
      label: '易翻翻译',
      explain: '命名模式：下划线；如: foo_bar',
      cmds: ['mmxh(下划线)'],
      anyCmds: [
        {
          type: 'over',
          label: '下划线',
        },
      ],
    },
  },
  {
    name: 'paramCase',
    handle: kebabCase,
    label: '中划线(小)',
    remark: '中划线(小), 例如: user-name',
    keyConfig: {
      code: 'codeMode__paramCase',
      label: '易翻翻译',
      explain: '命名模式：中划线(小)；如: foo-bar',
      cmds: ['mmzhx(中划线小)'],
      anyCmds: [
        {
          type: 'over',
          label: '中划线小',
        },
      ],
    },
  },
  {
    name: 'headerCase',
    handle: trainCase,
    label: '中划线(大)',
    remark: '中划线(大), 例如: User-Name',
    keyConfig: {
      code: 'codeMode__headerCase',
      label: '易翻翻译',
      explain: '命名模式：中划线(大)；如: Foo-Bar',
      cmds: ['mmzhd(中划线大)'],
      anyCmds: [
        {
          type: 'over',
          label: '中划线大',
        },
      ],
    },
  },
  {
    name: 'noCase',
    handle: noCase,
    label: '分词(小)',
    remark: '分词(小), 例如: user name',
    keyConfig: {
      code: 'codeMode__noCase',
      label: '易翻翻译',
      explain: '命名模式：分词(小)；如: foo bar',
      cmds: ['mmfcx(分词小)'],
      anyCmds: [
        {
          type: 'over',
          label: '分词小',
        },
      ],
    },
  },
  {
    name: 'capitalCase',
    handle: capitalCase,
    label: '分词(大)',
    remark: '分词(大), 例如: User Name',
    keyConfig: {
      code: 'codeMode__capitalCase',
      label: '易翻翻译',
      explain: '命名模式：分词(大)；如: Foo Bar',
      cmds: ['mmfcd(分词大)'],
      anyCmds: [
        {
          type: 'over',
          label: '分词大',
        },
      ],
    },
  },
  {
    name: 'dotCase',
    handle: dotCase,
    label: '对象属性',
    remark: '对象属性, 例如: user.name',
    keyConfig: {
      code: 'codeMode__dotCase',
      label: '易翻翻译',
      explain: '命名模式：对象属性；如: foo.bar',
      cmds: ['mmdx(对象属性)'],
      anyCmds: [
        {
          type: 'over',
          label: '对象属性',
        },
      ],
    },
  },
  {
    name: 'pathCase',
    handle: pathCase,
    label: '文件路径',
    remark: '文件路径, 例如: user/name',
    keyConfig: {
      code: 'codeMode__pathCase',
      label: '易翻翻译',
      explain: '命名模式：文件路径；如: foo/bar',
      cmds: ['mmwj(文件路径)'],
      anyCmds: [
        {
          type: 'over',
          label: '文件路径',
        },
      ],
    },
  },
  {
    name: 'constantCase',
    handle: constantCase,
    label: '常量(下划线)',
    remark: '常量(下划线), 例如: USER_NAME',
    keyConfig: {
      code: 'codeMode__constantCase',
      label: '易翻翻译',
      explain: '命名模式：常量；如: FOO_BAR',
      cmds: ['mmcl(常量)'],
      anyCmds: [
        {
          type: 'over',
          label: '常量',
        },
      ],
    },
  },
  {
    name: 'constantLineCase',
    handle: constantLineCase,
    label: '常量(中划线)',
    remark: '常量(中划线), 例如: USER-NAME',
    keyConfig: {
      code: 'codeMode__constantLineCase',
      label: '易翻翻译',
      explain: '命名模式：常量(中线)；如: FOO-BAR',
      cmds: ['mmclzx(常量中划线)'],
      anyCmds: [
        {
          type: 'over',
          label: '常量中线',
        },
      ],
    },
  },
  {
    name: 'startCase',
    handle: titleCase,
    label: '英文标题',
    remark: '英文标题, 例如: An English Headlin',
    keyConfig: {
      code: 'codeMode__startCase',
      label: '易翻翻译',
      explain: '命名模式：英文标题；如: An English Headline',
      cmds: ['mmbt(英文标题)', 'mmtitle(英文标题)'],
      anyCmds: [
        {
          type: 'over',
          label: '英文标题',
        },
      ],
    },
  },
]
