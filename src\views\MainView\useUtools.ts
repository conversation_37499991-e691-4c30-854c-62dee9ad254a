/** utools 业务逻辑 */
import { delay } from 'lodash-es'
import type { Ref } from 'vue'
import { useGlobalStore } from '@/store/globalData'
import { userStore } from '@/store/useUser'
import type { AnyFunction } from '@/types'

interface utoolsPluginEnter {
  code: string // plugin.json 配置的 feature.code
  type: string // plugin.json 配置的 feature.cmd.type, 可以为 "text"、"img"、 "files"、 "regex"、 "over"、"window"
  payload: any // feature.cmd.type 对应匹配的数据(String | Object | Array)
}
const utools = window?.utools

/**
 * 为了防止突然关闭utools太突兀, 所以延迟一小段时间关闭
 * @param delayTime 延迟时间
 */
export function 延迟关闭utools(delayTime = 300) {
  if (!utools) {
    return
  }
  return new Promise<void>((resolve) => {
    delay(() => {
      utools.hideMainWindow()
      resolve()
    }, delayTime)
  })
}

export async function 粘贴() {
  if (!utools) {
    return
  }
  const { 是否mac } = storeToRefs(useGlobalStore())
  const key = 是否mac.value ? 'command' : 'ctrl'
  await utools.simulateKeyboardTap('v', key)
}

export function useUtools(
  设置弹框Ref: Ref<any>,
  用户输入: Ref<string>,
  改变命名模式类型: AnyFunction,
  图片翻译组件: Ref<any>,
  // 去图片翻译: Function
) {
  function 去图片翻译(imgUrl?: string) {
    图片翻译组件.value.打开model(imgUrl)
  }

  // 初始化utools
  function utools初始化() {
    // 去图片翻译()

    const user = userStore()
    user.initUserInfo()

    if (!utools) {
      return
    }
    // 每当插件从后台进入到前台时, uTools 将会主动调用这个方法
    window.servers.onPluginEnter((params: utoolsPluginEnter) => {
      const { code, payload, type } = params
      设置弹框Ref.value.关闭弹窗()
      let value = ''
      if (type === 'over') {
        value = payload
      }

      用户输入.value = value

      // 开发环境
      // 用户输入.value = JSON.stringify(params)

      if (code === 'translateImg') {
        if (type === 'files') {
          // 传递图片文件进入图片翻译
          // {"code":"translateImg","type":"files","payload":[{"isFile":true,"isDirectory":false,"name":"test1.png","path":"/Users/<USER>/learn/utools/易翻/pictureTransNodeJsDemo/test1.png"}]}
        }
        else if (type === 'img') {
          // 传递图片数据进入图片翻译
          // {"code":"translateImg","type":"img","payload":"base64数据"}
          // 去图片翻译(payload as string)
          去图片翻译(payload as string)
        }
        else if (payload === '截图并翻译') {
          去图片翻译()
          图片翻译组件.value.截图并翻译()
        }
        else {
          // 去图片翻译
          去图片翻译()
        }
        return
      }
      else {
        图片翻译组件.value.modal取消()
      }
      改变命名模式类型(code)
    })
    utools.subInputBlur()
  }

  return {
    utools,
    utools初始化,
  }
}
