import { useGlobalStore } from '@/store/globalData'

const bodyDom = document.body // body的dom

function setPiniaData(params: 主题风格) {
  const globalStore = useGlobalStore()
  const { 当前主题 } = storeToRefs(globalStore)
  当前主题.value = params
}
export type 主题风格 = 'light' | 'dark'
/**
 * 设置主题
 * @param {string} val 'dark': 深色, 'light': 浅色
 * @param {boolean} disableTransition 是否禁用过渡动画
 */
export function setTheme(val: 主题风格, disableTransition = true) {
  setPiniaData(val)
  const 是否黑色 = val === 'dark'
  let style: HTMLStyleElement | undefined
  if (disableTransition) {
    style = document.createElement('style')
    style.appendChild(
      document.createTextNode('*,*::after,*::before{transition:none!important}'),
    )
    document.head.appendChild(style)
  }
  document.documentElement.classList.toggle('dark', 是否黑色)
  val === 'dark'
    ? bodyDom.setAttribute('arco-theme', 'dark')
    : bodyDom.removeAttribute('arco-theme')
  if (disableTransition) {
    // 调用getComputedStyle会强制浏览器重新绘制
    // @ts-expect-error unused variable
    const _ = window!.getComputedStyle(style!).opacity
    document.head.removeChild(style!)
  }
}
