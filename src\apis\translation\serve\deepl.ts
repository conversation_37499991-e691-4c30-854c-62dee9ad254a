/**
 * DeepL
 * https://www.deepl.com/docs-api
 */
import axios from 'axios'
import qs from 'qs'
import { type 翻译参数Type, 返回状态码及信息 } from '../common'

const last = {
  optionsStr: '',
  result: '',
}

/**
 * 通用翻译
 * @param {string} options.q 请求翻译query(UTF-8编码)
 * @param {string} options.from 翻译源语言(可设置为auto)
 * @param {string} options.to 翻译目标语言 (可设置为auto)
 * @param {object} options.keyConfig key配置
 */
export async function textTranslation({ q, from, to, keyConfig }: 翻译参数Type) {
  const { key, apiType, deeplxUrl, deeplxToken } = keyConfig

  const urlMap: any = {
    Free: `${import.meta.env.VITE_DEEPL_FREE}/v2/translate`,
    Pro: `${import.meta.env.VITE_DEEPL_PRO}/v2/translate`,
  }

  let url = ''
  const isDeeplx = apiType === 'deeplx'
  if (isDeeplx) {
    url = deeplxUrl
  }
  else {
    url = urlMap[apiType]
  }

  from = from === 'auto' ? '' : from
  const params: any = {
    text: q,
    source_lang: from,
    target_lang: to,
  }

  // deepl
  const formdata = isDeeplx ? params : qs.stringify(params)

  try {
    const apiRes = await axios(url, {
      method: 'post',
      data: formdata,
      headers: {
        'Authorization': isDeeplx ? `Bearer ${deeplxToken}` : `DeepL-Auth-Key ${key}`,
        // 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
        'Content-Type': isDeeplx ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf-8',
      },
    })

    let text = ''
    if (isDeeplx) {
      text = apiRes.data?.data
    }
    else {
      const { translations } = apiRes.data
      text = translations[0]?.text
    }
    const result = 返回状态码及信息(200, { text })

    last.result = result
    return result
  }
  catch (error: any) {
    const { status, data: errData } = error?.response || {}

    if (status === 403 || status === 401) {
      return 返回状态码及信息(400, null, '身份验证错误, 请检查一下密钥吧~')
    }

    const errMsg = errData?.message || `未知错误: ${error}`
    return 返回状态码及信息(500, null, errMsg)
  }
}
