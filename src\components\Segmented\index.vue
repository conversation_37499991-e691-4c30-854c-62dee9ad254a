<script setup lang="ts">
interface Props {
  modelValue: string | number
  options: Array<{
    label: string
    value: string | number
    disabled?: boolean
  }>
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const activeIndex = ref(0)
const segmentedRef = ref<HTMLDivElement>()
const indicatorRef = ref<HTMLDivElement>()

function handleSelect(value: string | number, index: number) {
  emit('update:modelValue', value)
  activeIndex.value = index
  nextTick(() => {
    updateIndicatorPosition()
  })
}

function updateIndicatorPosition() {
  if (!segmentedRef.value || !indicatorRef.value) { return }

  const items = segmentedRef.value.querySelectorAll('.segmented-item')
  const activeItem = items[activeIndex.value] as HTMLElement

  if (activeItem) {
    indicatorRef.value.style.left = `${activeItem.offsetLeft}px`
    indicatorRef.value.style.width = `${activeItem.offsetWidth}px`
  }
}

// 监听窗口大小变化，更新指示器位置
onMounted(() => {
  updateIndicatorPosition()
  window.addEventListener('resize', updateIndicatorPosition)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateIndicatorPosition)
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  const index = props.options.findIndex(option => option.value === newValue)
  if (index !== -1) {
    activeIndex.value = index
    nextTick(() => {
      updateIndicatorPosition()
    })
  }
})
</script>

<template>
  <div
    ref="segmentedRef"
    class="segmented-container"
    role="tablist"
  >
    <div
      v-for="(option, index) in options"
      :key="option.value"
      class="segmented-item"
      :class="{
        active: modelValue === option.value,
        disabled: option.disabled,
      }"
      role="tab"
      :aria-selected="modelValue === option.value"
      :tabindex="option.disabled ? -1 : 0"
      @click="!option.disabled && handleSelect(option.value, index)"
      @keydown.enter="!option.disabled && handleSelect(option.value, index)"
      @keydown.space="!option.disabled && handleSelect(option.value, index)"
    >
      {{ option.label }}
    </div>
    <div
      ref="indicatorRef"
      class="segmented-indicator"
      aria-hidden="true"
    />
  </div>
</template>

<style scoped lang="scss">
.segmented-container {
  position: relative;
  display: inline-flex;
  background-color: var(--el-fill-color-light);
  border-radius: 6px;
  padding: 2px;
  gap: 2px;
  user-select: none;
}

.segmented-item {
  position: relative;
  padding: 4px 12px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 1;
  white-space: nowrap;

  &:hover:not(.disabled) {
    color: var(--el-color-primary);
  }

  &.active {
    color: var(--el-color-primary);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.segmented-indicator {
  position: absolute;
  height: calc(100% - 4px);
  background-color: white;
  border-radius: 4px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 10%);
  pointer-events: none;
  top: 2px;
}
</style>
