[{"Name": "Microsoft Server Speech Text to Speech Voice (af-ZA, AdriNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "af-ZA-AdriNeural", "Gender": "Female", "Locale": "af-ZA", "LocaleName": "Afrikaans (South Africa)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (af-ZA, WillemNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "af-ZA-WillemNeural", "Gender": "Male", "Locale": "af-ZA", "LocaleName": "Afrikaans (South Africa)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (am-ET, AmehaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "አምሀ", "ShortName": "am-ET-AmehaNeural", "Gender": "Male", "Locale": "am-ET", "LocaleName": "Amharic (Ethiopia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (am-ET, MekdesNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "መቅደስ", "ShortName": "am-ET-MekdesNeural", "Gender": "Female", "Locale": "am-ET", "LocaleName": "Amharic (Ethiopia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-AE, FatimaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "فاطمة", "ShortName": "ar-AE-FatimaNeural", "Gender": "Female", "Locale": "ar-AE", "LocaleName": "Arabic (United Arab Emirates)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-A<PERSON>, HamdanNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "حمدان", "ShortName": "ar-AE-HamdanNeural", "Gender": "Male", "Locale": "ar-AE", "LocaleName": "Arabic (United Arab Emirates)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-BH, AliNeural)", "DisplayName": "<PERSON>", "LocalName": "علي", "ShortName": "ar-BH-AliNeural", "Gender": "Male", "Locale": "ar-BH", "LocaleName": "Arabic (Bahrain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-BH, LailaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "ليلى", "ShortName": "ar-BH-<PERSON><PERSON>eural", "Gender": "Female", "Locale": "ar-BH", "LocaleName": "Arabic (Bahrain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-DZ, AminaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "أمينة", "ShortName": "ar-DZ-AminaNeural", "Gender": "Female", "Locale": "ar-DZ", "LocaleName": "Arabic (Algeria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-DZ, IsmaelNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "إسماعيل", "ShortName": "ar-DZ-Ismael<PERSON>eural", "Gender": "Male", "Locale": "ar-DZ", "LocaleName": "Arabic (Algeria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-EG, SalmaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "سلمى", "ShortName": "ar-EG-SalmaNeural", "Gender": "Female", "Locale": "ar-EG", "LocaleName": "Arabic (Egypt)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, ShakirNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "شاكر", "ShortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ar-EG", "LocaleName": "Arabic (Egypt)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-EG, Hoda)", "DisplayName": "Hoda", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "ar-EG-Hoda", "Gender": "Female", "Locale": "ar-EG", "LocaleName": "Arabic (Egypt)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, BasselNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "باسل", "ShortName": "ar-IQ-BasselNeural", "Gender": "Male", "Locale": "ar-IQ", "LocaleName": "Arabic (Iraq)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, RanaNeural)", "DisplayName": "<PERSON>", "LocalName": "رنا", "ShortName": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "ar-IQ", "LocaleName": "Arabic (Iraq)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-J<PERSON>, SanaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "سناء", "ShortName": "ar-JO-SanaNeural", "Gender": "Female", "Locale": "ar-JO", "LocaleName": "Arabic (Jordan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-JO, TaimNeural)", "DisplayName": "Taim", "LocalName": "تيم", "ShortName": "ar-JO-TaimNeural", "Gender": "Male", "Locale": "ar-JO", "LocaleName": "Arabic (Jordan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-K<PERSON>, FahedNeural)", "DisplayName": "<PERSON>ahed", "LocalName": "ف<PERSON><PERSON>", "ShortName": "ar-KW-FahedNeural", "Gender": "Male", "Locale": "ar-KW", "LocaleName": "Arabic (Kuwait)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-K<PERSON>, NouraNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "نورا", "ShortName": "ar-KW-NouraNeural", "Gender": "Female", "Locale": "ar-KW", "LocaleName": "Arabic (Kuwait)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-L<PERSON>, ImanNeural)", "DisplayName": "Iman", "LocalName": "إيمان", "ShortName": "ar-LY-ImanNeural", "Gender": "Female", "Locale": "ar-LY", "LocaleName": "Arabic (Libya)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (a<PERSON><PERSON><PERSON><PERSON>, OmarNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "ar-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ar-LY", "LocaleName": "Arabic (Libya)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON>, Jamal<PERSON><PERSON>)", "DisplayName": "<PERSON>", "LocalName": "جمال", "ShortName": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ar-<PERSON>", "LocaleName": "Arabic (Morocco)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-MA, MounaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "من<PERSON>", "ShortName": "ar-<PERSON><PERSON><PERSON><PERSON>N<PERSON><PERSON>", "Gender": "Female", "Locale": "ar-<PERSON>", "LocaleName": "Arabic (Morocco)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-QA, AmalNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "ar-QA-AmalNeural", "Gender": "Female", "Locale": "ar-QA", "LocaleName": "Arabic (Qatar)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-QA, MoazNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "معاذ", "ShortName": "ar-QA-MoazNeural", "Gender": "Male", "Locale": "ar-QA", "LocaleName": "Arabic (Qatar)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-SA, HamedNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "ar-SA-HamedNeural", "Gender": "Male", "Locale": "ar-SA", "LocaleName": "Arabic (Saudi Arabia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-SA, ZariyahNeural)", "DisplayName": "Zariyah", "LocalName": "زارية", "ShortName": "ar-SA-ZariyahNeural", "Gender": "Female", "Locale": "ar-SA", "LocaleName": "Arabic (Saudi Arabia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-SA, Naayf)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "نايف", "ShortName": "ar-SA-Naayf", "Gender": "Male", "Locale": "ar-SA", "LocaleName": "Arabic (Saudi Arabia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-S<PERSON>, AmanyNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "أ<PERSON><PERSON>ي", "ShortName": "ar-SY-AmanyNeural", "Gender": "Female", "Locale": "ar-SY", "LocaleName": "Arabic (Syria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, LaithNeural)", "DisplayName": "<PERSON>th", "LocalName": "ليث", "ShortName": "ar-S<PERSON>-<PERSON>thNeural", "Gender": "Male", "Locale": "ar-SY", "LocaleName": "Arabic (Syria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-T<PERSON>, HediNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "هادي", "ShortName": "ar-TN-HediNeural", "Gender": "Male", "Locale": "ar-TN", "LocaleName": "Arabic (Tunisia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-TN, ReemNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "ريم", "ShortName": "ar-TN-ReemNeural", "Gender": "Female", "Locale": "ar-TN", "LocaleName": "Arabic (Tunisia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "مريم", "ShortName": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "ar-YE", "LocaleName": "Arabic (Yemen)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ar-Y<PERSON>, SalehNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "صالح", "ShortName": "ar-YE-SalehNeural", "Gender": "Male", "Locale": "ar-YE", "LocaleName": "Arabic (Yemen)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (bg-BG, BorislavNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "Борислав", "ShortName": "bg-BG-<PERSON><PERSON>Neural", "Gender": "Male", "Locale": "bg-BG", "LocaleName": "Bulgarian (Bulgaria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (bg-BG, KalinaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "Калина", "ShortName": "bg-BG-KalinaNeural", "Gender": "Female", "Locale": "bg-BG", "LocaleName": "Bulgarian (Bulgaria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (bg-<PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "bg-BG-Ivan", "Gender": "Male", "Locale": "bg-BG", "LocaleName": "Bulgarian (Bulgaria)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (bn-BD, NabanitaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "নবনীতা", "ShortName": "bn-BD-NabanitaNeural", "Gender": "Female", "Locale": "bn-BD", "LocaleName": "Bangla (Bangladesh)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (bn-BD, PradeepNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "প্রদ<PERSON><PERSON><PERSON>প", "ShortName": "bn-BD-<PERSON><PERSON>epNeural", "Gender": "Male", "Locale": "bn-BD", "LocaleName": "Bangla (Bangladesh)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (bn-IN, BashkarNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "ভাস্কর", "ShortName": "bn-IN-BashkarNeural", "Gender": "Male", "Locale": "bn-IN", "LocaleName": "Bengali (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (bn-IN, TanishaaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "তানিশা", "ShortName": "bn-IN-<PERSON><PERSON>aaNeural", "Gender": "Female", "Locale": "bn-IN", "LocaleName": "Bengali (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ca<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "ca-ES-<PERSON><PERSON>", "Gender": "Female", "Locale": "ca-ES", "LocaleName": "Catalan (Spain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ca-ES, AlbaNeural)", "DisplayName": "Alba", "LocalName": "Alba", "ShortName": "ca-ES-AlbaNeural", "Gender": "Female", "Locale": "ca-ES", "LocaleName": "Catalan (Spain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ca-ES, EnricNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "ca-ES-EnricNeural", "Gender": "Male", "Locale": "ca-ES", "LocaleName": "Catalan (Spain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ca-ES, HerenaRUS)", "DisplayName": "<PERSON>na", "LocalName": "Helena", "ShortName": "ca-ES-HerenaRUS", "Gender": "Female", "Locale": "ca-ES", "LocaleName": "Catalan (Spain)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (cs-CZ, AntoninNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "cs-CZ-<PERSON>inNeural", "Gender": "Male", "Locale": "cs-CZ", "LocaleName": "Czech (Czech)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (cs-CZ, VlastaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "cs-CZ-VlastaNeural", "Gender": "Female", "Locale": "cs-CZ", "LocaleName": "Czech (Czech)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (cs-CZ, Jakub)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "cs-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "cs-CZ", "LocaleName": "Czech (Czech)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (cy-GB, AledNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "cy-GB-AledNeural", "Gender": "Male", "Locale": "cy-GB", "LocaleName": "Welsh (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (cy-GB, NiaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "cy-GB-NiaNeural", "Gender": "Female", "Locale": "cy-GB", "LocaleName": "Welsh (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (da<PERSON><PERSON><PERSON>, ChristelNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "da-DK-<PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "da-DK", "LocaleName": "Danish (Denmark)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (da-<PERSON><PERSON>, JeppeNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "da-D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "da-DK", "LocaleName": "Danish (Denmark)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (da-DK, HelleRUS)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "da-DK-HelleRUS", "Gender": "Female", "Locale": "da-DK", "LocaleName": "Danish (Denmark)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, IngridNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-AT-IngridNeural", "Gender": "Female", "Locale": "de-AT", "LocaleName": "German (Austria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, JonasNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-AT-JonasNeural", "Gender": "Male", "Locale": "de-AT", "LocaleName": "German (Austria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-AT-<PERSON>", "Gender": "Male", "Locale": "de-AT", "LocaleName": "German (Austria)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, JanNeural)", "DisplayName": "Jan", "LocalName": "Jan", "ShortName": "de-CH-Jan<PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "de-CH", "LocaleName": "German (Switzerland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-CH, LeniNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-CH-LeniNeural", "Gender": "Female", "Locale": "de-CH", "LocaleName": "German (Switzerland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "de-CH", "LocaleName": "German (Switzerland)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-Katja<PERSON>eural", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, ConradNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-DE-ConradNeural", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, AmalaNeural)", "DisplayName": "Amala", "LocalName": "Amala", "ShortName": "de-DE-AmalaNeural", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, BerndNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-BerndNeural", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, ChristophNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-DE-ChristophNeural", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, ElkeNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-ElkeNeural", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, GiselaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "de-DE-GiselaNeural", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, KasperNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-KasperNeural", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON>ian<PERSON>al)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-<PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, KlarissaNeural)", "DisplayName": "Klarissa", "LocalName": "Klarissa", "ShortName": "de-DE-KlarissaNeural", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, KlausNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-DE-KlausN<PERSON>", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, LouisaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-DE-LouisaNeural", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, MajaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-Maj<PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, RalfNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-RalfNeural", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, TanjaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "de-DE-TanjaNeural", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (de-DE, HeddaRUS)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "de-DE-HeddaRUS", "Gender": "Female", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "de-DE<PERSON><PERSON>", "Gender": "Male", "Locale": "de-DE", "LocaleName": "German (Germany)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (el-GR, AthinaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "Αθηνά", "ShortName": "el-GR-AthinaNeural", "Gender": "Female", "Locale": "el-GR", "LocaleName": "Greek (Greece)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (el-GR, NestorasNeural)", "DisplayName": "Nestoras", "LocalName": "Νέστορας", "ShortName": "el-GR-NestorasNeural", "Gender": "Male", "Locale": "el-GR", "LocaleName": "Greek (Greece)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "Στέφανος", "ShortName": "el-GR-Stefanos", "Gender": "Male", "Locale": "el-GR", "LocaleName": "Greek (Greece)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-AU", "LocaleName": "English (Australia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, WilliamNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-AU", "LocaleName": "English (Australia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-AU-<PERSON>", "Gender": "Female", "Locale": "en-AU", "LocaleName": "English (Australia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-AU-HayleyRUS", "Gender": "Female", "Locale": "en-AU", "LocaleName": "English (Australia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-CA, ClaraNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-CA-ClaraNeural", "Gender": "Female", "Locale": "en-CA", "LocaleName": "English (Canada)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, LiamNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-CA-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-CA", "LocaleName": "English (Canada)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-CA-HeatherRUS", "Gender": "Female", "Locale": "en-CA", "LocaleName": "English (Canada)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-CA-<PERSON>", "Gender": "Female", "Locale": "en-CA", "LocaleName": "English (Canada)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-LibbyNeural", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, RyanNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-RyanN<PERSON><PERSON>", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, SoniaN<PERSON>al)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, AbbiNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "en-GB-AbbiNeural", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, AlfieNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-GB-AlfieNeural", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, BellaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-BellaNeural", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, ElliotNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, EthanNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-EthanNeural", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, HollieNeural)", "DisplayName": "<PERSON>llie", "LocalName": "<PERSON>llie", "ShortName": "en-GB-HollieNeural", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, <PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-GB-<PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, NoahNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-NoahNeural", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, OliverNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-OliverNeural", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, OliviaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, ThomasNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, MiaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-MiaNeural", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-<PERSON>", "Gender": "Male", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-GB, HazelRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-HazelRUS", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-GB-<PERSON>", "Gender": "Female", "Locale": "en-GB", "LocaleName": "English (United Kingdom)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-HK, SamNeural)", "DisplayName": "Sam", "LocalName": "Sam", "ShortName": "en-HK-SamNeural", "Gender": "Male", "Locale": "en-HK", "LocaleName": "English (Hongkong)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-HK, YanNeural)", "DisplayName": "Yan", "LocalName": "Yan", "ShortName": "en-HK-YanNeural", "Gender": "Female", "Locale": "en-HK", "LocaleName": "English (Hongkong)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, ConnorNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-IE-Connor<PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-IE", "LocaleName": "English (Ireland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, EmilyN<PERSON><PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-IE-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-IE", "LocaleName": "English (Ireland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-IE-<PERSON>", "Gender": "Male", "Locale": "en-IE", "LocaleName": "English (Ireland)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-IN, NeerjaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "en-IN-NeerjaNeural", "Gender": "Female", "Locale": "en-IN", "LocaleName": "English (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-IN, PrabhatNeural)", "DisplayName": "Prab<PERSON>", "LocalName": "Prab<PERSON>", "ShortName": "en-IN-PrabhatNeural", "Gender": "Male", "Locale": "en-IN", "LocaleName": "English (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, <PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-IN-Heera", "Gender": "Female", "Locale": "en-IN", "LocaleName": "English (India)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-IN-PriyaRUS", "Gender": "Female", "Locale": "en-IN", "LocaleName": "English (India)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-IN-Ravi", "Gender": "Male", "Locale": "en-IN", "LocaleName": "English (India)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, AsiliaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-KE-AsiliaNeural", "Gender": "Female", "Locale": "en-KE", "LocaleName": "English (Kenya)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-KE, ChilembaNeural)", "DisplayName": "Chilemba", "LocalName": "Chilemba", "ShortName": "en-KE-ChilembaNeural", "Gender": "Male", "Locale": "en-KE", "LocaleName": "English (Kenya)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-NG, AbeoNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-NG-AbeoNeural", "Gender": "Male", "Locale": "en-NG", "LocaleName": "English (Nigeria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-NG, EzinneNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "en-NG-EzinneNeural", "Gender": "Female", "Locale": "en-NG", "LocaleName": "English (Nigeria)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-NZ, MitchellNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-NZ-MitchellNeural", "Gender": "Male", "Locale": "en-NZ", "LocaleName": "English (New Zealand)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, MollyNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-NZ-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-NZ", "LocaleName": "English (New Zealand)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, JamesNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-PH-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-PH", "LocaleName": "English (Philippines)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-PH, RosaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-PH-RosaNeural", "Gender": "Female", "Locale": "en-PH", "LocaleName": "English (Philippines)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-SG, LunaNeural)", "DisplayName": "Luna", "LocalName": "Luna", "ShortName": "en-SG-LunaNeural", "Gender": "Female", "Locale": "en-SG", "LocaleName": "English (Singapore)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, WayneNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-SG-Wayne<PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-SG", "LocaleName": "English (Singapore)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-TZ, ElimuNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-TZ-ElimuNeural", "Gender": "Male", "Locale": "en-TZ", "LocaleName": "English (Tanzania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-TZ, ImaniNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-TZ-ImaniNeural", "Gender": "Female", "Locale": "en-TZ", "LocaleName": "English (Tanzania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, JennyNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["assistant", "chat", "customerservice", "newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, JennyMultilingualNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-JennyMultilingualNeural", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SecondaryLocaleList": ["de-DE", "en-AU", "en-CA", "en-GB", "es-ES", "es-MX", "fr-CA", "fr-FR", "it-IT", "ja-<PERSON>", "ko-KR", "pt-BR", "zh-CN"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-GuyN<PERSON><PERSON>", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA", "WordPerMinute": {"default": "215", "newscast": "210"}}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, AmberNeural)", "DisplayName": "Amber", "LocalName": "Amber", "ShortName": "en-US-AmberN<PERSON><PERSON>", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, AnaNeural)", "DisplayName": "Ana", "LocalName": "Ana", "ShortName": "en-US-AnaNeural", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "DisplayName": "Aria", "LocalName": "Aria", "ShortName": "en-US-AriaNeural", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["chat", "customerservice", "narration-professional", "newscast-casual", "newscast-formal", "cheerful", "empathetic", "angry", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, AshleyNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA", "WordPerMinute": {"default": "149"}}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, BrandonNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-BrandonN<PERSON><PERSON>", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, ChristopherNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, CoraNeural)", "DisplayName": "Cora", "LocalName": "Cora", "ShortName": "en-US-CoraNeural", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, ElizabethNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, EricNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, JacobNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, MichelleNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON>", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, MonicaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, SaraNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-SaraNeural", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, DavisNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-DavisNeural", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["chat", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, JaneNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, JasonNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, NancyNeural)", "DisplayName": "Nancy", "LocalName": "Nancy", "ShortName": "en-US-NancyNeural", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, TonyNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "StyleList": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaRUS)", "DisplayName": "Aria", "LocalName": "Aria", "ShortName": "en-US-AriaRUS", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, BenjaminRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-BenjaminRUS", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-US-GuyRUS", "Gender": "Male", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "24000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-US, ZiraRUS)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "en-US-ZiraRUS", "Gender": "Female", "Locale": "en-US", "LocaleName": "English (United States)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, LeahNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-ZA-LeahNeural", "Gender": "Female", "Locale": "en-ZA", "LocaleName": "English (South Africa)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (en-ZA, LukeNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "en-ZA-LukeNeural", "Gender": "Male", "Locale": "en-ZA", "LocaleName": "English (South Africa)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-AR, ElenaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-AR-ElenaNeural", "Gender": "Female", "Locale": "es-AR", "LocaleName": "Spanish (Argentina)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-AR, TomasNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-AR-TomasNeural", "Gender": "Male", "Locale": "es-AR", "LocaleName": "Spanish (Argentina)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-B<PERSON>, MarceloNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-BO-MarceloNeural", "Gender": "Male", "Locale": "es-BO", "LocaleName": "Spanish (Bolivia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-BO, SofiaNeural)", "DisplayName": "Sofia", "LocalName": "Sofia", "ShortName": "es-BO-SofiaNeural", "Gender": "Female", "Locale": "es-BO", "LocaleName": "Spanish (Bolivia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CL, CatalinaNeural)", "DisplayName": "Catalina", "LocalName": "Catalina", "ShortName": "es-CL-CatalinaNeural", "Gender": "Female", "Locale": "es-CL", "LocaleName": "Spanish (Chile)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CL, LorenzoNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-CL-LorenzoNeural", "Gender": "Male", "Locale": "es-CL", "LocaleName": "Spanish (Chile)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CO, GonzaloNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "es-CO-GonzaloNeural", "Gender": "Male", "Locale": "es-CO", "LocaleName": "Spanish (Colombia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CO, SalomeNeural)", "DisplayName": "Salome", "LocalName": "Salome", "ShortName": "es-CO-SalomeNeural", "Gender": "Female", "Locale": "es-CO", "LocaleName": "Spanish (Colombia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CR, JuanNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-CR-JuanNeural", "Gender": "Male", "Locale": "es-CR", "LocaleName": "Spanish (Costa Rica)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CR, MariaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-CR-MariaNeural", "Gender": "Female", "Locale": "es-CR", "LocaleName": "Spanish (Costa Rica)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CU, BelkysNeural)", "DisplayName": "Belkys", "LocalName": "Belkys", "ShortName": "es-CU-BelkysNeural", "Gender": "Female", "Locale": "es-CU", "LocaleName": "Spanish (Cuba)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-CU, ManuelNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-CU-ManuelNeural", "Gender": "Male", "Locale": "es-CU", "LocaleName": "Spanish (Cuba)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-D<PERSON>, EmilioNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-DO-EmilioNeural", "Gender": "Male", "Locale": "es-DO", "LocaleName": "Spanish (Dominican Republic)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-D<PERSON>, Ramona<PERSON>al)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-DO-RamonaNeural", "Gender": "Female", "Locale": "es-DO", "LocaleName": "Spanish (Dominican Republic)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-EC, AndreaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-EC-AndreaNeural", "Gender": "Female", "Locale": "es-EC", "LocaleName": "Spanish (Ecuador)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-EC, LuisNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-EC-LuisNeural", "Gender": "Male", "Locale": "es-EC", "LocaleName": "Spanish (Ecuador)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-ES, AlvaroNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "es-ES-AlvaroNeural", "Gender": "Male", "Locale": "es-ES", "LocaleName": "Spanish (Spain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "es-ES-Elvira<PERSON>eural", "Gender": "Female", "Locale": "es-ES", "LocaleName": "Spanish (Spain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-ES, HelenaRUS)", "DisplayName": "Helena", "LocalName": "Helena", "ShortName": "es-ES-HelenaRUS", "Gender": "Female", "Locale": "es-ES", "LocaleName": "Spanish (Spain)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON><PERSON>, Laura)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-ES-Laura", "Gender": "Female", "Locale": "es-ES", "LocaleName": "Spanish (Spain)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON><PERSON>, Pablo)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-ES-Pablo", "Gender": "Male", "Locale": "es-ES", "LocaleName": "Spanish (Spain)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-GQ, JavierNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-GQ-JavierNeural", "Gender": "Male", "Locale": "es-GQ", "LocaleName": "Spanish (Equatorial Guinea)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-G<PERSON>, TeresaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-GQ-Teresa<PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "es-GQ", "LocaleName": "Spanish (Equatorial Guinea)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-GT, AndresNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON>", "ShortName": "es-GT-AndresNeural", "Gender": "Male", "Locale": "es-GT", "LocaleName": "Spanish (Guatemala)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-GT, MartaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-GT-MartaNeural", "Gender": "Female", "Locale": "es-GT", "LocaleName": "Spanish (Guatemala)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-HN, CarlosNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-HN-CarlosNeural", "Gender": "Male", "Locale": "es-HN", "LocaleName": "Spanish (Honduras)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-HN, KarlaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-HN-Karla<PERSON>eural", "Gender": "Female", "Locale": "es-HN", "LocaleName": "Spanish (Honduras)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "DisplayName": "Dal<PERSON>", "LocalName": "Dal<PERSON>", "ShortName": "es-MX-DaliaNeural", "Gender": "Female", "Locale": "es-MX", "LocaleName": "Spanish (Mexico)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-MX, JorgeNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-MX-JorgeNeural", "Gender": "Male", "Locale": "es-MX", "LocaleName": "Spanish (Mexico)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-MX, HildaRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-MX-HildaRUS", "Gender": "Female", "Locale": "es-MX", "LocaleName": "Spanish (Mexico)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-MX, Raul)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-MX-Raul", "Gender": "Male", "Locale": "es-MX", "LocaleName": "Spanish (Mexico)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-NI, FedericoNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-NI-FedericoNeural", "Gender": "Male", "Locale": "es-NI", "LocaleName": "Spanish (Nicaragua)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-NI, YolandaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "es-NI-Yo<PERSON>aNeural", "Gender": "Female", "Locale": "es-NI", "LocaleName": "Spanish (Nicaragua)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-PA, MargaritaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "es-PA-MargaritaNeural", "Gender": "Female", "Locale": "es-PA", "LocaleName": "Spanish (Panama)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-PA, RobertoNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-PA-RobertoNeural", "Gender": "Male", "Locale": "es-PA", "LocaleName": "Spanish (Panama)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-PE, AlexNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-PE-AlexNeural", "Gender": "Male", "Locale": "es-PE", "LocaleName": "Spanish (Peru)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-PE, CamilaNeural)", "DisplayName": "Camila", "LocalName": "Camila", "ShortName": "es-PE-CamilaNeural", "Gender": "Female", "Locale": "es-PE", "LocaleName": "Spanish (Peru)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-<PERSON>, <PERSON>a<PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-PR-Karin<PERSON>", "Gender": "Female", "Locale": "es-PR", "LocaleName": "Spanish (Puerto Rico)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-PR, VictorNeural)", "DisplayName": "Victor", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "es-PR-VictorNeural", "Gender": "Male", "Locale": "es-PR", "LocaleName": "Spanish (Puerto Rico)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-PY, MarioNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-PY-<PERSON>", "Gender": "Male", "Locale": "es-PY", "LocaleName": "Spanish (Paraguay)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-PY, TaniaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-PY-TaniaNeural", "Gender": "Female", "Locale": "es-PY", "LocaleName": "Spanish (Paraguay)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-SV, LorenaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-SV-LorenaNeural", "Gender": "Female", "Locale": "es-SV", "LocaleName": "Spanish (El Salvador)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-SV, RodrigoNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-SV-RodrigoNeural", "Gender": "Male", "Locale": "es-SV", "LocaleName": "Spanish (El Salvador)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-US, AlonsoNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-US-AlonsoNeural", "Gender": "Male", "Locale": "es-US", "LocaleName": "Spanish (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-US, PalomaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "es-US-PalomaNeural", "Gender": "Female", "Locale": "es-US", "LocaleName": "Spanish (United States)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-UY, MateoNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "es-UY-MateoNeural", "Gender": "Male", "Locale": "es-UY", "LocaleName": "Spanish (Uruguay)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-UY, ValentinaNeural)", "DisplayName": "Valentina", "LocalName": "Valentina", "ShortName": "es-UY-ValentinaNeural", "Gender": "Female", "Locale": "es-UY", "LocaleName": "Spanish (Uruguay)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-VE, PaolaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "es-VE-PaolaNeural", "Gender": "Female", "Locale": "es-VE", "LocaleName": "Spanish (Venezuela)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (es-V<PERSON>, SebastianNeural)", "DisplayName": "<PERSON>", "LocalName": "Sebastián", "ShortName": "es-VE-SebastianNeural", "Gender": "Male", "Locale": "es-VE", "LocaleName": "Spanish (Venezuela)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (et-EE, AnuNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "et-EE-AnuNeural", "Gender": "Female", "Locale": "et-EE", "LocaleName": "Estonian (Estonia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (et-EE, KertNeural)", "DisplayName": "Kert", "LocalName": "Kert", "ShortName": "et-EE-KertNeural", "Gender": "Male", "Locale": "et-EE", "LocaleName": "Estonian (Estonia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fa-IR, DilaraNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "دلارا", "ShortName": "fa-IR-DilaraNeural", "Gender": "Female", "Locale": "fa-IR", "LocaleName": "Persian (Iran)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fa-IR, FaridNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "فرید", "ShortName": "fa-IR-FaridNeural", "Gender": "Male", "Locale": "fa-IR", "LocaleName": "Persian (Iran)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fi-FI, SelmaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "fi-FI-SelmaNeural", "Gender": "Female", "Locale": "fi-FI", "LocaleName": "Finnish (Finland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fi-FI, HarriNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "fi-FI-HarriNeural", "Gender": "Male", "Locale": "fi-FI", "LocaleName": "Finnish (Finland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fi-FI, NooraNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "fi-FI-NooraNeural", "Gender": "Female", "Locale": "fi-FI", "LocaleName": "Finnish (Finland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fi-FI, HeidiRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fi-FI-HeidiRUS", "Gender": "Female", "Locale": "fi-FI", "LocaleName": "Finnish (Finland)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fil-PH, AngeloNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fil-PH-AngeloNeural", "Gender": "Male", "Locale": "fil-PH", "LocaleName": "Filipino (Philippines)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fil-PH, BlessicaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fil-PH-BlessicaNeural", "Gender": "Female", "Locale": "fil-PH", "LocaleName": "Filipino (Philippines)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, CharlineNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fr-BE-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "fr-BE", "LocaleName": "French (Belgium)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr<PERSON><PERSON><PERSON>, GerardN<PERSON>al)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-B<PERSON>-<PERSON><PERSON>", "Gender": "Male", "Locale": "fr-BE", "LocaleName": "French (Belgium)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fr-CA-<PERSON><PERSON>vie<PERSON>eural", "Gender": "Female", "Locale": "fr-CA", "LocaleName": "French (Canada)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-CA, AntoineNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-CA-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "fr-CA", "LocaleName": "French (Canada)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON>, JeanNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "fr-CA", "LocaleName": "French (Canada)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON>, Caroline)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-CA-<PERSON>", "Gender": "Female", "Locale": "fr-CA", "LocaleName": "French (Canada)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-CA, HarmonieRUS)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fr-CA-HarmonieRUS", "Gender": "Female", "Locale": "fr-CA", "LocaleName": "French (Canada)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-CH, ArianeNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "fr-CH-ArianeNeural", "Gender": "Female", "Locale": "fr-CH", "LocaleName": "French (Switzerland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-CH, FabriceNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fr-CH-FabriceNeural", "Gender": "Male", "Locale": "fr-CH", "LocaleName": "French (Switzerland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON>, Guillaume)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-CH-Guillaume", "Gender": "Male", "Locale": "fr-CH", "LocaleName": "French (Switzerland)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON>", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "StyleList": ["cheerful", "sad"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-F<PERSON>, HenriNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, AlainN<PERSON>al)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-FR, BrigitteNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fr-FR-B<PERSON>itteNeural", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-FR, CelesteNeural)", "DisplayName": "Celeste", "LocalName": "Celeste", "ShortName": "fr-FR-CelesteNeural", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, ClaudeN<PERSON>al)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-FR, <PERSON>ie<PERSON>al)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "fr-FR-<PERSON><PERSON>", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-FR, EloiseNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "fr-FR-EloiseNeural", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, JacquelineNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, JeromeN<PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON>", "Gender": "Male", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-FR, JosephineNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, MauriceN<PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON><PERSON>", "Gender": "Male", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-F<PERSON>, YvesNeural)", "DisplayName": "Yves", "LocalName": "Yves", "ShortName": "fr-FR-YvesNeural", "Gender": "Male", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-FR, YvetteNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fr-FR-YvetteNeural", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-FR, HortenseRUS)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "fr-FR-HortenseRUS", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-Julie", "Gender": "Female", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "fr-FR-<PERSON>", "Gender": "Male", "Locale": "fr-FR", "LocaleName": "French (France)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ga-IE, ColmNeural)", "DisplayName": "Colm", "LocalName": "Colm", "ShortName": "ga-IE-ColmNeural", "Gender": "Male", "Locale": "ga-IE", "LocaleName": "Irish (Ireland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ga-IE, OrlaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "ga-IE-OrlaNeural", "Gender": "Female", "Locale": "ga-IE", "LocaleName": "Irish (Ireland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (gl-ES, RoiNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "gl-ES-RoiNeural", "Gender": "Male", "Locale": "gl-ES", "LocaleName": "Galicia<PERSON> (Spain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (gl-ES, SabelaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "gl-ES-SabelaNeural", "Gender": "Female", "Locale": "gl-ES", "LocaleName": "Galicia<PERSON> (Spain)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (gu-IN, DhwaniNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "ધ્વની", "ShortName": "gu-IN-DhwaniNeural", "Gender": "Female", "Locale": "gu-IN", "LocaleName": "Gujarati (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (gu-IN, NiranjanNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "નિરંજન", "ShortName": "gu-IN-NiranjanNeural", "Gender": "Male", "Locale": "gu-IN", "LocaleName": "Gujarati (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (he-IL, AvriNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "א<PERSON><PERSON>י", "ShortName": "he-IL-AvriNeural", "Gender": "Male", "Locale": "he-IL", "LocaleName": "Hebrew (Israel)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (he-IL, HilaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "הילה", "ShortName": "he-IL-HilaNeural", "Gender": "Female", "Locale": "he-IL", "LocaleName": "Hebrew (Israel)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (he-IL, Asaf)", "DisplayName": "<PERSON><PERSON>", "LocalName": "אסף", "ShortName": "he-IL-Asaf", "Gender": "Male", "Locale": "he-IL", "LocaleName": "Hebrew (Israel)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hi-<PERSON>, <PERSON><PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>र", "ShortName": "hi-IN-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "hi-IN", "LocaleName": "Hindi (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hi-IN, SwaraNeural)", "DisplayName": "Swara", "LocalName": "स्वरा", "ShortName": "hi-IN-SwaraNeural", "Gender": "Female", "Locale": "hi-IN", "LocaleName": "Hindi (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hi-<PERSON>, <PERSON><PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "हेमन्त", "ShortName": "hi-IN-<PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "hi-IN", "LocaleName": "Hindi (India)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hi-IN, Kalpana)", "DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "LocalName": "कल्पना", "ShortName": "hi-IN-Kalpana", "Gender": "Female", "Locale": "hi-IN", "LocaleName": "Hindi (India)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hr-HR, GabrijelaNeural)", "DisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShortName": "hr-HR-<PERSON><PERSON><PERSON><PERSON>laNeural", "Gender": "Female", "Locale": "hr-HR", "LocaleName": "Croatian (Croatia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hr-HR, SreckoNeural)", "DisplayName": "Srecko", "LocalName": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "hr-HR-SreckoNeural", "Gender": "Male", "Locale": "hr-HR", "LocaleName": "Croatian (Croatia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hr-H<PERSON>, Matej)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "hr-<PERSON><PERSON>-<PERSON><PERSON>", "Gender": "Male", "Locale": "hr-HR", "LocaleName": "Croatian (Croatia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hu-HU, NoemiNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "hu-HU-NoemiNeural", "Gender": "Female", "Locale": "hu-HU", "LocaleName": "Hungarian (Hungary)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hu-HU, TamasNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "Tamás", "ShortName": "hu-HU-TamasNeural", "Gender": "Male", "Locale": "hu-HU", "LocaleName": "Hungarian (Hungary)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (hu-HU, Szabolcs)", "DisplayName": "Szabolcs", "LocalName": "Szabolcs", "ShortName": "hu-HU-Szabolcs", "Gender": "Male", "Locale": "hu-HU", "LocaleName": "Hungarian (Hungary)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (id-ID, ArdiNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "id-ID-ArdiNeural", "Gender": "Male", "Locale": "id-ID", "LocaleName": "Indonesian (Indonesia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (id-ID, GadisNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "id-ID-GadisNeural", "Gender": "Female", "Locale": "id-ID", "LocaleName": "Indonesian (Indonesia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (id-ID, Andika)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "id-ID-<PERSON><PERSON>", "Gender": "Male", "Locale": "id-ID", "LocaleName": "Indonesian (Indonesia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (is-IS, GudrunNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShortName": "is-IS-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "is-IS", "LocaleName": "Icelandic (Iceland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (is-IS, GunnarNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "is-IS-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "is-IS", "LocaleName": "Icelandic (Iceland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (it-IT, IsabellaNeural)", "DisplayName": "Isabella", "LocalName": "Isabella", "ShortName": "it-IT-IsabellaNeural", "Gender": "Female", "Locale": "it-IT", "LocaleName": "Italian (Italy)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (it-IT, DiegoNeural)", "DisplayName": "Diego", "LocalName": "Diego", "ShortName": "it-IT-DiegoNeural", "Gender": "Male", "Locale": "it-IT", "LocaleName": "Italian (Italy)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "it-IT-ElsaNeural", "Gender": "Female", "Locale": "it-IT", "LocaleName": "Italian (Italy)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (it-IT, Cosimo)", "DisplayName": "Cosimo", "LocalName": "Cosimo", "ShortName": "it-IT-Cosimo", "Gender": "Male", "Locale": "it-IT", "LocaleName": "Italian (Italy)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (it-IT, LuciaRUS)", "DisplayName": "Lucia", "LocalName": "Lucia", "ShortName": "it-IT-LuciaRUS", "Gender": "Female", "Locale": "it-IT", "LocaleName": "Italian (Italy)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "七海", "ShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "ja-<PERSON>", "LocaleName": "Japanese (Japan)", "StyleList": ["chat", "customerservice", "cheerful"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ja<PERSON><PERSON>, KeitaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "圭太", "ShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ja-<PERSON>", "LocaleName": "Japanese (Japan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "歩美", "ShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "ja-<PERSON>", "LocaleName": "Japanese (Japan)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ja-JP, HarukaRUS)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "春香", "ShortName": "ja-<PERSON>-HarukaRUS", "Gender": "Female", "Locale": "ja-<PERSON>", "LocaleName": "Japanese (Japan)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "一郎", "ShortName": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ja-<PERSON>", "LocaleName": "Japanese (Japan)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (jv-ID, DimasNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "jv-ID-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "jv-ID", "LocaleName": "Javanese (Indonesia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (jv-ID, SitiNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "jv-ID-SitiNeural", "Gender": "Female", "Locale": "jv-ID", "LocaleName": "Javanese (Indonesia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (kk-KZ, AigulNeural)", "DisplayName": "Aigul", "LocalName": "Айгүл", "ShortName": "kk-KZ-AigulNeural", "Gender": "Female", "Locale": "kk-KZ", "LocaleName": "Kazakh (Kazakhstan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (kk-KZ, DauletNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "Дәулет", "ShortName": "kk-KZ-DauletNeural", "Gender": "Male", "Locale": "kk-KZ", "LocaleName": "Kazakh (Kazakhstan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (km-KH, PisethNeural)", "DisplayName": "Piseth", "LocalName": "ពិសិដ្ឋ", "ShortName": "km-KH-PisethNeural", "Gender": "Male", "Locale": "km-KH", "LocaleName": "Khmer (Cambodia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (km-KH, SreymomNeural)", "DisplayName": "Sreymom", "LocalName": "ស្រីមុំ", "ShortName": "km-KH-SreymomNeural", "Gender": "Female", "Locale": "km-KH", "LocaleName": "Khmer (Cambodia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (kn-<PERSON>, GaganNeural)", "DisplayName": "Gaga<PERSON>", "LocalName": "ಗಗನ್", "ShortName": "kn-IN-GaganNeural", "Gender": "Male", "Locale": "kn-IN", "LocaleName": "Kannada (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (kn-IN, SapnaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "ಸಪ್ನಾ", "ShortName": "kn-IN-Sa<PERSON>naNeural", "Gender": "Female", "Locale": "kn-IN", "LocaleName": "Kannada (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "DisplayName": "Sun-Hi", "LocalName": "선히", "ShortName": "ko-KR-SunHiNeural", "Gender": "Female", "Locale": "ko-KR", "LocaleName": "Korean (Korea)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ko-KR, InJoonNeural)", "DisplayName": "InJoon", "LocalName": "인준", "ShortName": "ko-KR-InJoonNeural", "Gender": "Male", "Locale": "ko-KR", "LocaleName": "Korean (Korea)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ko-KR, HeamiRUS)", "DisplayName": "<PERSON><PERSON>", "LocalName": "해 미", "ShortName": "ko-KR-HeamiRUS", "Gender": "Female", "Locale": "ko-KR", "LocaleName": "Korean (Korea)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (lo-LA, ChanthavongNeural)", "DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "LocalName": "ຈັນທະວົງ", "ShortName": "lo-LA-ChanthavongNeural", "Gender": "Male", "Locale": "lo-LA", "LocaleName": "<PERSON> (Laos)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (lo-LA, KeomanyNeural)", "DisplayName": "Keomany", "LocalName": "ແກ້ວມະນີ", "ShortName": "lo-LA-KeomanyNeural", "Gender": "Female", "Locale": "lo-LA", "LocaleName": "<PERSON> (Laos)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (lt-LT, LeonasNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "lt-LT-LeonasNeural", "Gender": "Male", "Locale": "lt-LT", "LocaleName": "Lithuanian (Lithuania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (lt-LT, OnaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "lt-LT-OnaNeural", "Gender": "Female", "Locale": "lt-LT", "LocaleName": "Lithuanian (Lithuania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (lv-LV, EveritaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "lv-LV-EveritaNeural", "Gender": "Female", "Locale": "lv-LV", "LocaleName": "Latvian (Latvia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (lv-LV, NilsNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "lv-LV-NilsNeural", "Gender": "Male", "Locale": "lv-LV", "LocaleName": "Latvian (Latvia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (mk-M<PERSON>, AleksandarNeural)", "DisplayName": "Aleksandar", "LocalName": "Александар", "ShortName": "mk-MK-AleksandarNeural", "Gender": "Male", "Locale": "mk-MK", "LocaleName": "Macedonian (Republic of North Macedonia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (mk-MK, MarijaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "Марија", "ShortName": "mk-MK-MarijaNeural", "Gender": "Female", "Locale": "mk-MK", "LocaleName": "Macedonian (Republic of North Macedonia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ml-IN, MidhunNeural)", "DisplayName": "Midhun", "LocalName": "മിഥുൻ", "ShortName": "ml-IN-MidhunNeural", "Gender": "Male", "Locale": "ml-IN", "LocaleName": "Malayalam (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ml-IN, SobhanaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "ശോഭന", "ShortName": "ml-IN-SobhanaNeural", "Gender": "Female", "Locale": "ml-IN", "LocaleName": "Malayalam (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (mr-IN, AarohiNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "आरोही", "ShortName": "mr-<PERSON>-<PERSON><PERSON>hiNeural", "Gender": "Female", "Locale": "mr-<PERSON>", "LocaleName": "Marathi (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (mr-<PERSON>, ManoharNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "मनो<PERSON>र", "ShortName": "mr-<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "mr-<PERSON>", "LocaleName": "Marathi (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ms-MY, OsmanNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ms-MY", "LocaleName": "<PERSON> (Malaysia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ms<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>al)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "ms-MY", "LocaleName": "<PERSON> (Malaysia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ms-MY, Rizwan)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "ms-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ms-MY", "LocaleName": "<PERSON> (Malaysia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (mt-MT, GraceNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "mt-MT-GraceNeural", "Gender": "Female", "Locale": "mt-MT", "LocaleName": "Maltese (Malta)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (mt-M<PERSON>, JosephN<PERSON>al)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "mt-MT-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "mt-MT", "LocaleName": "Maltese (Malta)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (my-MM, NilarNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "နီလာ", "ShortName": "my-MM-<PERSON><PERSON><PERSON><PERSON>al", "Gender": "Female", "Locale": "my-MM", "LocaleName": "Burmese (Myanmar)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (my-MM, ThihaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "သီဟ", "ShortName": "my-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "my-MM", "LocaleName": "Burmese (Myanmar)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, <PERSON>nilleNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "nb-NO-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "nb-NO", "LocaleName": "Norwegian (Bokmål, Norway)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, FinnNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "nb-NO-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "nb-NO", "LocaleName": "Norwegian (Bokmål, Norway)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, IselinNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "nb-NO-IselinNeural", "Gender": "Female", "Locale": "nb-NO", "LocaleName": "Norwegian (Bokmål, Norway)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nb-NO, HuldaRUS)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "nb-NO-HuldaRUS", "Gender": "Female", "Locale": "nb-NO", "LocaleName": "Norwegian (Bokmål, Norway)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nl-B<PERSON>, ArnaudNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "nl-BE-<PERSON><PERSON>udNeural", "Gender": "Male", "Locale": "nl-BE", "LocaleName": "Dutch (Belgium)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nl-BE, DenaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "nl-BE-DenaNeural", "Gender": "Female", "Locale": "nl-BE", "LocaleName": "Dutch (Belgium)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nl-NL, ColetteNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "nl-NL-ColetteNeural", "Gender": "Female", "Locale": "nl-NL", "LocaleName": "Dutch (Netherlands)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nl-NL, FennaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "nl-NL-FennaNeural", "Gender": "Female", "Locale": "nl-NL", "LocaleName": "Dutch (Netherlands)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nl-NL, MaartenNeural)", "DisplayName": "Ma<PERSON>n", "LocalName": "Ma<PERSON>n", "ShortName": "nl-NL-MaartenNeural", "Gender": "Male", "Locale": "nl-NL", "LocaleName": "Dutch (Netherlands)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "nl-NL-HannaRUS", "Gender": "Female", "Locale": "nl-NL", "LocaleName": "Dutch (Netherlands)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pl-PL, AgnieszkaNeural)", "DisplayName": "A<PERSON><PERSON>z<PERSON>", "LocalName": "A<PERSON><PERSON>z<PERSON>", "ShortName": "pl-PL-AgnieszkaNeural", "Gender": "Female", "Locale": "pl-PL", "LocaleName": "Polish (Poland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pl-PL, MarekNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "pl-PL-MarekNeural", "Gender": "Male", "Locale": "pl-PL", "LocaleName": "Polish (Poland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pl-PL, ZofiaNeural)", "DisplayName": "Zofia", "LocalName": "Zofia", "ShortName": "pl-PL-ZofiaNeural", "Gender": "Female", "Locale": "pl-PL", "LocaleName": "Polish (Poland)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "DisplayName": "Paul<PERSON>", "LocalName": "Paul<PERSON>", "ShortName": "pl-PL-PaulinaRUS", "Gender": "Female", "Locale": "pl-PL", "LocaleName": "Polish (Poland)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ps-AF, GulNawazNeural)", "DisplayName": "Gul Nawaz", "LocalName": " ګل نواز", "ShortName": "ps-AF-GulNawazNeural", "Gender": "Male", "Locale": "ps-AF", "LocaleName": "<PERSON><PERSON><PERSON> (Afghanistan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ps-AF, LatifaNeural)", "DisplayName": "Latifa", "LocalName": "لطيفه", "ShortName": "ps-AF-LatifaNeural", "Gender": "Female", "Locale": "ps-AF", "LocaleName": "<PERSON><PERSON><PERSON> (Afghanistan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "DisplayName": "Francisca", "LocalName": "Francisca", "ShortName": "pt-BR-FranciscaNeural", "Gender": "Female", "Locale": "pt-BR", "LocaleName": "Portuguese (Brazil)", "StyleList": ["calm"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-BR, AntonioNeural)", "DisplayName": "Antonio", "LocalName": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "pt-BR-AntonioNeural", "Gender": "Male", "Locale": "pt-BR", "LocaleName": "Portuguese (Brazil)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-<PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "pt-BR-<PERSON>", "Gender": "Male", "Locale": "pt-BR", "LocaleName": "Portuguese (Brazil)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-BR, HeloisaRUS)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "pt-BR-HeloisaRUS", "Gender": "Female", "Locale": "pt-BR", "LocaleName": "Portuguese (Brazil)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-PT, DuarteNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "pt-PT-DuarteNeural", "Gender": "Male", "Locale": "pt-PT", "LocaleName": "Portuguese (Portugal)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-PT, FernandaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "pt-PT-FernandaNeural", "Gender": "Female", "Locale": "pt-PT", "LocaleName": "Portuguese (Portugal)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-PT, <PERSON>quel<PERSON>eural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "pt-<PERSON>-<PERSON><PERSON>", "Gender": "Female", "Locale": "pt-PT", "LocaleName": "Portuguese (Portugal)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (pt-PT, HeliaRUS)", "DisplayName": "Helia", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "pt-PT-HeliaRUS", "Gender": "Female", "Locale": "pt-PT", "LocaleName": "Portuguese (Portugal)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ro<PERSON>R<PERSON>, AlinaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "ro-RO-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "ro-RO", "LocaleName": "Romanian (Romania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, EmilNeural)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "ro-RO-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ro-RO", "LocaleName": "Romanian (Romania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "ro-RO-<PERSON>", "Gender": "Male", "Locale": "ro-RO", "LocaleName": "Romanian (Romania)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ru-R<PERSON>, SvetlanaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "Светлана", "ShortName": "ru-RU-<PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "ru-RU", "LocaleName": "Russian (Russia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ru-R<PERSON>, DariyaNeural)", "DisplayName": "Dariya", "LocalName": "Дар<PERSON>я", "ShortName": "ru-RU-DariyaNeural", "Gender": "Female", "Locale": "ru-RU", "LocaleName": "Russian (Russia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (r<PERSON>-<PERSON><PERSON>, DmitryNeural)", "DisplayName": "Dmitry", "LocalName": "Д<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShortName": "ru-RU-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ru-RU", "LocaleName": "Russian (Russia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "DisplayName": "Ekat<PERSON>", "LocalName": "Екатерина", "ShortName": "ru-RU-EkaterinaRUS", "Gender": "Female", "Locale": "ru-RU", "LocaleName": "Russian (Russia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ru-<PERSON><PERSON>, <PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON>", "LocalName": "Ирина", "ShortName": "ru-RU-<PERSON><PERSON>", "Gender": "Female", "Locale": "ru-RU", "LocaleName": "Russian (Russia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>)", "DisplayName": "<PERSON>", "LocalName": "Павел", "ShortName": "ru-RU-<PERSON>", "Gender": "Male", "Locale": "ru-RU", "LocaleName": "Russian (Russia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (si-LK, SameeraNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "සමීර", "ShortName": "si-LK-SameeraNeural", "Gender": "Male", "Locale": "si-LK", "LocaleName": "Sinhala (Sri Lanka)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (si-LK, ThiliniNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "තිළිණි", "ShortName": "si-LK-ThiliniNeural", "Gender": "Female", "Locale": "si-LK", "LocaleName": "Sinhala (Sri Lanka)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sk-SK, LukasNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "sk-SK-LukasNeural", "Gender": "Male", "Locale": "sk-SK", "LocaleName": "Slovak (Slovakia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sk-SK, ViktoriaNeural)", "DisplayName": "Viktoria", "LocalName": "Viktória", "ShortName": "sk-SK-ViktoriaNeural", "Gender": "Female", "Locale": "sk-SK", "LocaleName": "Slovak (Slovakia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sk-SK, Filip)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "sk-SK-Filip", "Gender": "Male", "Locale": "sk-SK", "LocaleName": "Slovak (Slovakia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sl-SI, PetraNeural)", "DisplayName": "Petra", "LocalName": "Petra", "ShortName": "sl-SI-PetraNeural", "Gender": "Female", "Locale": "sl-SI", "LocaleName": "Slovenian (Slovenia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sl-SI, RokNeural)", "DisplayName": "Rok", "LocalName": "Rok", "ShortName": "sl-SI-RokNeural", "Gender": "Male", "Locale": "sl-SI", "LocaleName": "Slovenian (Slovenia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sl-SI, Lado)", "DisplayName": "Lado", "LocalName": "Lado", "ShortName": "sl-SI-Lado", "Gender": "Male", "Locale": "sl-SI", "LocaleName": "Slovenian (Slovenia)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (so-SO, MuuseNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "so-SO-MuuseNeural", "Gender": "Male", "Locale": "so-SO", "LocaleName": "Somali (Somalia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (so-SO, UbaxNeural)", "DisplayName": "Ubax", "LocalName": "Ubax", "ShortName": "so-SO-UbaxNeural", "Gender": "Female", "Locale": "so-SO", "LocaleName": "Somali (Somalia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sr-<PERSON>, NicholasN<PERSON>al)", "DisplayName": "<PERSON>", "LocalName": "Никола", "ShortName": "sr-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "sr-RS", "LocaleName": "Serbian (Serbia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sr-RS, SophieNeural)", "DisplayName": "<PERSON>", "LocalName": "Софија", "ShortName": "sr-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Female", "Locale": "sr-RS", "LocaleName": "Serbian (Serbia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (su-ID, JajangNeural)", "DisplayName": "Jajang", "LocalName": "Jajang", "ShortName": "su-ID-JajangNeural", "Gender": "Male", "Locale": "su-ID", "LocaleName": "<PERSON><PERSON><PERSON> (Indonesia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (su-ID, TutiNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "su-ID-TutiNeural", "Gender": "Female", "Locale": "su-ID", "LocaleName": "<PERSON><PERSON><PERSON> (Indonesia)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sv-SE, SofieNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "sv-SE-SofieNeural", "Gender": "Female", "Locale": "sv-SE", "LocaleName": "Swedish (Sweden)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sv-SE, HilleviNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "sv-SE-HilleviNeural", "Gender": "Female", "Locale": "sv-SE", "LocaleName": "Swedish (Sweden)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sv-SE, MattiasNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "sv-SE-Mattias<PERSON>al", "Gender": "Male", "Locale": "sv-SE", "LocaleName": "Swedish (Sweden)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sv-SE, HedvigRUS)", "DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "sv-SE-HedvigRUS", "Gender": "Female", "Locale": "sv-SE", "LocaleName": "Swedish (Sweden)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sw-K<PERSON>, RafikiNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "sw-KE-RafikiNeural", "Gender": "Male", "Locale": "sw-KE", "LocaleName": "<PERSON><PERSON><PERSON><PERSON> (Kenya)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sw-K<PERSON>, ZuriNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "sw-KE-ZuriNeural", "Gender": "Female", "Locale": "sw-KE", "LocaleName": "<PERSON><PERSON><PERSON><PERSON> (Kenya)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sw-TZ, DaudiNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "sw-TZ-DaudiNeural", "Gender": "Male", "Locale": "sw-TZ", "LocaleName": "<PERSON><PERSON><PERSON><PERSON> (Tanzania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (sw-TZ, RehemaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "<PERSON><PERSON><PERSON>", "ShortName": "sw-TZ-RehemaNeural", "Gender": "Female", "Locale": "sw-TZ", "LocaleName": "<PERSON><PERSON><PERSON><PERSON> (Tanzania)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ta-IN, PallaviNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "பல்லவி", "ShortName": "ta-IN-PallaviNeural", "Gender": "Female", "Locale": "ta-IN", "LocaleName": "Tamil (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ta-IN, ValluvarNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "வள்ளுவர்", "ShortName": "ta-IN-ValluvarNeural", "Gender": "Male", "Locale": "ta-IN", "LocaleName": "Tamil (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ta-IN, Valluvar)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "வள்ளுவர்", "ShortName": "ta-IN-Valluvar", "Gender": "Male", "Locale": "ta-IN", "LocaleName": "Tamil (India)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ta<PERSON>L<PERSON>, KumarNeural)", "DisplayName": "<PERSON>", "LocalName": "குமார்", "ShortName": "ta-LK-KumarNeural", "Gender": "Male", "Locale": "ta-LK", "LocaleName": "Tamil (Sri Lanka)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ta-LK, SaranyaNeural)", "DisplayName": "Saranya", "LocalName": "சரண்யா", "ShortName": "ta-LK-SaranyaNeural", "Gender": "Female", "Locale": "ta-LK", "LocaleName": "Tamil (Sri Lanka)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ta-SG, AnbuNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "அன்பு", "ShortName": "ta-SG-AnbuNeural", "Gender": "Male", "Locale": "ta-SG", "LocaleName": "Tamil (Singapore)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ta-SG, VenbaNeural)", "DisplayName": "Venba", "LocalName": "வெண்பா", "ShortName": "ta-SG-VenbaNeural", "Gender": "Female", "Locale": "ta-SG", "LocaleName": "Tamil (Singapore)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (te-<PERSON>, MohanNeural)", "DisplayName": "<PERSON>", "LocalName": "మోహన్", "ShortName": "te-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "te-IN", "LocaleName": "Telugu (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (te-IN, ShrutiNeural)", "DisplayName": "Shruti", "LocalName": "శ్రుతి", "ShortName": "te-IN-ShrutiNeural", "Gender": "Female", "Locale": "te-IN", "LocaleName": "Telugu (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (te-<PERSON>, <PERSON><PERSON>)", "DisplayName": "Chitra", "LocalName": "చిత్ర", "ShortName": "te-IN-Chitra", "Gender": "Female", "Locale": "te-IN", "LocaleName": "Telugu (India)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (th-T<PERSON>, PremwadeeNeural)", "DisplayName": "Premwadee", "LocalName": "เปรมวดี", "ShortName": "th-TH-<PERSON><PERSON><PERSON><PERSON>eNeural", "Gender": "Female", "Locale": "th-TH", "LocaleName": "<PERSON> (Thailand)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (th-TH, AcharaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "อัจฉรา", "ShortName": "th-TH-A<PERSON>raNeural", "Gender": "Female", "Locale": "th-TH", "LocaleName": "<PERSON> (Thailand)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (th-TH, NiwatNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "นิวัฒน์", "ShortName": "th-TH-NiwatNeural", "Gender": "Male", "Locale": "th-TH", "LocaleName": "<PERSON> (Thailand)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (th-T<PERSON>, Pattara)", "DisplayName": "Pattara", "LocalName": "ภัทรา", "ShortName": "th-TH-Pattara", "Gender": "Male", "Locale": "th-TH", "LocaleName": "<PERSON> (Thailand)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (tr-TR, AhmetNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "tr-TR-AhmetNeural", "Gender": "Male", "Locale": "tr-TR", "LocaleName": "Turkish (Turkey)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "tr-TR-EmelNeural", "Gender": "Female", "Locale": "tr-TR", "LocaleName": "Turkish (Turkey)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (tr-TR, SedaRUS)", "DisplayName": "Seda", "LocalName": "Seda", "ShortName": "tr-TR-SedaRUS", "Gender": "Female", "Locale": "tr-TR", "LocaleName": "Turkish (Turkey)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (uk-UA, OstapNeural)", "DisplayName": "Ostap", "LocalName": "Остап", "ShortName": "uk-UA-OstapNeural", "Gender": "Male", "Locale": "uk-UA", "LocaleName": "Ukrainian (Ukraine)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (uk-UA, PolinaNeural)", "DisplayName": "Pol<PERSON>", "LocalName": "Пол<PERSON>на", "ShortName": "uk-UA-PolinaNeural", "Gender": "Female", "Locale": "uk-UA", "LocaleName": "Ukrainian (Ukraine)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ur-IN, GulNeural)", "DisplayName": "Gul", "LocalName": "گل", "ShortName": "ur-IN-GulNeural", "Gender": "Female", "Locale": "ur-IN", "LocaleName": "Urdu (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ur-<PERSON>, SalmanNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "سلمان", "ShortName": "ur-IN-SalmanN<PERSON><PERSON>", "Gender": "Male", "Locale": "ur-IN", "LocaleName": "Urdu (India)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ur-<PERSON><PERSON>, AsadNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "اسد", "ShortName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Gender": "Male", "Locale": "ur-PK", "LocaleName": "Urdu (Pakistan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (ur-P<PERSON>, UzmaNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "عظمیٰ", "ShortName": "ur-PK-UzmaNeural", "Gender": "Female", "Locale": "ur-PK", "LocaleName": "Urdu (Pakistan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (uz-UZ, MadinaNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "uz-UZ-MadinaNeural", "Gender": "Female", "Locale": "uz-UZ", "LocaleName": "Uzbek (Uzbekistan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (uz-UZ, SardorNeural)", "DisplayName": "Sardor", "LocalName": "Sardor", "ShortName": "uz-UZ-SardorNeural", "Gender": "Male", "Locale": "uz-UZ", "LocaleName": "Uzbek (Uzbekistan)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (vi-VN, HoaiMyNeural)", "DisplayName": "HoaiMy", "LocalName": "Hoài My", "ShortName": "vi-VN-HoaiMyNeural", "Gender": "Female", "Locale": "vi-VN", "LocaleName": "Vietnamese (Vietnam)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (vi-VN, NamMinhNeural)", "DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "LocalName": "<PERSON>", "ShortName": "vi-VN-Nam<PERSON>inhNeural", "Gender": "Male", "Locale": "vi-VN", "LocaleName": "Vietnamese (Vietnam)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (vi-VN, An)", "DisplayName": "An", "LocalName": "An", "ShortName": "vi-VN-An", "Gender": "Male", "Locale": "vi-VN", "LocaleName": "Vietnamese (Vietnam)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "晓晓", "ShortName": "zh-CN-XiaoxiaoNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["assistant", "chat", "customerservice", "newscast", "affectionate", "angry", "calm", "cheerful", "disgruntled", "fearful", "gentle", "lyrical", "sad", "serious", "poetry-reading"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "DisplayName": "Yunyang", "LocalName": "云扬", "ShortName": "zh-CN-YunyangNeural", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["customerservice", "narration-professional", "newscast-casual"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, XiaochenNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "晓辰", "ShortName": "zh-CN-<PERSON><PERSON>N<PERSON>al", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>h<PERSON><PERSON><PERSON>, XiaohanNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "晓涵", "ShortName": "zh-C<PERSON>-<PERSON><PERSON>Neural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "gentle", "affectionate", "embarrassed"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaomoNeural)", "DisplayName": "Xiao<PERSON>", "LocalName": "晓墨", "ShortName": "zh-CN-XiaomoNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "affectionate", "gentle", "envious"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA", "RolePlayList": ["YoungAdultFemale", "YoungAdultMale", "OlderAdultFemale", "OlderAdultMale", "<PERSON><PERSON><PERSON><PERSON>", "SeniorMale", "Girl", "Boy"]}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoqiuNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "晓秋", "ShortName": "zh-CN-XiaoqiuNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoruiNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "晓睿", "ShortName": "zh-CN-XiaoruiNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["calm", "fearful", "angry", "sad"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoshuangNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "晓双", "ShortName": "zh-CN-XiaoshuangNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["chat"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoxuanNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "晓萱", "ShortName": "zh-CN-XiaoxuanNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "gentle", "depressed"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA", "RolePlayList": ["YoungAdultFemale", "YoungAdultMale", "OlderAdultFemale", "OlderAdultMale", "<PERSON><PERSON><PERSON><PERSON>", "SeniorMale", "Girl", "Boy"]}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyanNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "晓颜", "ShortName": "zh-CN-<PERSON>yanNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyouNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "晓悠", "ShortName": "zh-CN-XiaoyouNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunxiNeural)", "DisplayName": "Yunxi", "LocalName": "云希", "ShortName": "zh-CN-YunxiNeural", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["narration-relaxed", "embarrassed", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "chat", "assistant", "newscast"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA", "RolePlayList": ["Narrator", "YoungAdultMale", "Boy"]}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunyeNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "云野", "ShortName": "zh-CN-YunyeNeural", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA", "RolePlayList": ["YoungAdultFemale", "YoungAdultMale", "OlderAdultFemale", "OlderAdultMale", "<PERSON><PERSON><PERSON><PERSON>", "SeniorMale", "Girl", "Boy"]}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN-L<PERSON>, XiaobeiNeural)", "DisplayName": "Xiaobei", "LocalName": "晓北辽宁", "ShortName": "zh-CN-LN-XiaobeiNeural", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN-SC, YunxiNeural)", "DisplayName": "YunxiSichuan", "LocalName": "云希四川", "ShortName": "zh-CN-SC-YunxiNeural", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunfengNeural)", "DisplayName": "Yunfeng", "LocalName": "云枫", "ShortName": "zh-CN-YunfengNeural", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["angry", "disgruntled", "cheerful", "fearful", "sad", "serious", "depressed"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunhaoNeural)", "DisplayName": "Yunhao", "LocalName": "云皓", "ShortName": "zh-CN-YunhaoNeural", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["Advertisement_upbeat"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunjianNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "云健", "ShortName": "zh-CN-YunjianNeural", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "StyleList": ["Narration-relaxed", "Sports_commentary", "Sports_commentary_excited"], "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "Preview"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN, HuihuiRUS)", "DisplayName": "Hui<PERSON>", "LocalName": "慧慧", "ShortName": "zh-CN-HuihuiRUS", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN, Kangkang)", "DisplayName": "Kangkang", "LocalName": "康康", "ShortName": "zh-CN-Kangkang", "Gender": "Male", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-CN, Yaoyao)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "瑶瑶", "ShortName": "zh-CN-Yaoyao", "Gender": "Female", "Locale": "zh-CN", "LocaleName": "Chinese (Mandarin, Simplified)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-HK, HiuMaanNeural)", "DisplayName": "HiuMaan", "LocalName": "曉曼", "ShortName": "zh-HK-HiuMaanNeural", "Gender": "Female", "Locale": "zh-HK", "LocaleName": "Chinese (Cantonese, Traditional)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-HK, HiuGaaiNeural)", "DisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LocalName": "曉佳", "ShortName": "zh-HK-HiuGaaiNeural", "Gender": "Female", "Locale": "zh-HK", "LocaleName": "Chinese (Cantonese, Traditional)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-HK, WanLungNeural)", "DisplayName": "Wan<PERSON><PERSON>", "LocalName": "雲龍", "ShortName": "zh-HK-WanLungNeural", "Gender": "Male", "Locale": "zh-HK", "LocaleName": "Chinese (Cantonese, Traditional)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (<PERSON>h-<PERSON>, Danny)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "zh-HK-Danny", "Gender": "Male", "Locale": "zh-HK", "LocaleName": "Chinese (Cantonese, Traditional)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "DisplayName": "<PERSON>", "LocalName": "<PERSON>", "ShortName": "zh-HK-TracyRUS", "Gender": "Female", "Locale": "zh-HK", "LocaleName": "Chinese (Cantonese, Traditional)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-T<PERSON>, HsiaoChenNeural)", "DisplayName": "HsiaoChen", "LocalName": "曉臻", "ShortName": "zh-TW-HsiaoChenNeural", "Gender": "Female", "Locale": "zh-TW", "LocaleName": "Chinese (Taiwanese Mandarin)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HsiaoYuNeural)", "DisplayName": "HsiaoYu", "LocalName": "曉雨", "ShortName": "zh-TW-HsiaoYuNeural", "Gender": "Female", "Locale": "zh-TW", "LocaleName": "Chinese (Taiwanese Mandarin)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, YunJheNeural)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "雲哲", "ShortName": "zh-TW-YunJheNeural", "Gender": "Male", "Locale": "zh-TW", "LocaleName": "Chinese (Taiwanese Mandarin)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "涵涵", "ShortName": "zh-TW-HanHanRUS", "Gender": "Female", "Locale": "zh-TW", "LocaleName": "Chinese (Taiwanese Mandarin)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-T<PERSON>, Ya<PERSON>)", "DisplayName": "Yating", "LocalName": "雅婷", "ShortName": "zh-TW-Yating", "Gender": "Female", "Locale": "zh-TW", "LocaleName": "Chinese (Taiwanese Mandarin)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zh-T<PERSON>, <PERSON><PERSON><PERSON>)", "DisplayName": "<PERSON><PERSON><PERSON>", "LocalName": "志威", "ShortName": "zh-TW-Zhiwei", "Gender": "Male", "Locale": "zh-TW", "LocaleName": "Chinese (Taiwanese Mandarin)", "SampleRateHertz": "16000", "VoiceType": "Standard", "Status": "Deprecated"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zu-ZA, ThandoNeural)", "DisplayName": "<PERSON><PERSON>", "LocalName": "<PERSON><PERSON>", "ShortName": "zu-ZA-ThandoNeural", "Gender": "Female", "Locale": "zu-ZA", "LocaleName": "Zulu (South Africa)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}, {"Name": "Microsoft Server Speech Text to Speech Voice (zu-ZA, ThembaNeural)", "DisplayName": "Themba", "LocalName": "Themba", "ShortName": "zu-ZA-ThembaNeural", "Gender": "Male", "Locale": "zu-ZA", "LocaleName": "Zulu (South Africa)", "SampleRateHertz": "24000", "VoiceType": "<PERSON><PERSON><PERSON>", "Status": "GA"}]