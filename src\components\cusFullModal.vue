<template>
  <a-modal
    v-bind="$attrs"
    :mask-closable="false"
    modal-class="no_header_modal"
    :unmount-on-close="true"
    fullscreen
    title-align="start"
    body-class="p-0 overflow-hidden h-full"
    :footer="false"
    @open="wrapper的y滚动 = 1"
  >
    <div ref="modal包裹Ref" class="h-full h-full flex flex-col overflow-auto">
      <section
        ref="设置弹框HeaderRef"
        border="b-1px dark:#333"
        position="sticky top-0 z-102"
        p="r-18px l-4px"
        text="16px"
        class="textured_glass min-h-44px w-full flex-y-c justify-between backdrop-blur-5px transition-all-400"
        :class="[
          wrapper的y滚动 > 16
            && 'shadow-md !border-b-transparent dark:(shadow-lg !border-#414141)',
        ]"
      >
        <div
          class="matte_feather_wrapper h-44px rounded-8px px-16px horizontal-place-8px"
        >
          <img src="/favicon.svg" width="24">
          <slot name="header_left" />
        </div>
        <div>
          <slot name="header_right" />
        </div>
      </section>
      <div class="flex-1">
        <slot
          :modal-size-info="{
            headerHeight: 设置弹框Header高度 + 侧边paddingY,
            footerHeight: 设置弹框Footer高度 + 侧边paddingY + 1,
          }"
        >
          弹窗内容
        </slot>
      </div>
      <section
        ref="设置弹框FooterRef"
        position="sticky bottom-0 z-102"
        border="t-solid t-width-1px #e5e6eb dark:#333"
        bg="#fff/70 dark:#2a2a2b/80"
        class="textured_glass min-h-56px flex-y-c justify-between px-20px backdrop-blur-8px"
      >
        <div class="matte_feather_wrapper h-full flex-y-c px-16px -translate-x-16px">
          <slot name="footer_left" />
        </div>
        <div class="space-x-12px">
          <slot name="footer_right" />
        </div>
      </section>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
const modal包裹Ref = ref()
const 设置弹框HeaderRef = ref()
const 设置弹框FooterRef = ref()
const { height: 设置弹框Header高度 } = useElementSize(设置弹框HeaderRef)
const { height: 设置弹框Footer高度 } = useElementSize(设置弹框FooterRef)
const { y: wrapper的y滚动 } = useScroll(modal包裹Ref, { behavior: 'smooth' })
const 侧边paddingY = 16
</script>

<style lang="scss" scoped>
@use '@/styles/scssVar.scss';

.textured_glass {
  // background: radial-gradient(transparent, var(--setting-header-color) 2px); // 网孔效果
  background: linear-gradient(
    45deg,
    transparent,
    var(--setting-header-color) 3.6px
  ); // 方形棱角玻璃效果

  // ↓这里的important不可以去除, 因为background在深色模式需要覆盖, 但是background又包含-size
  // 如果不加这个important, 那么此处的background-size将被深色的background覆盖
  background-size: 5px 5px !important;
}

.matte_feather_wrapper {
  @include scssVar.matte-feather;
}
</style>
