// 最近使用的语言选择
import { getDbStorageItem, setDbStorageItem } from '@/utils/storage'
import { 用户配置存储 } from '@MainView/MainViewData'

export function 添加历史记录(from: string, to: string) {
  const 用户配置 = 用户配置存储()

  const lastFromTo = getDbStorageItem('lastFromTo', [])
  const setValue = `${from}>>${to}`
  // 重复处理
  const existingIndex = lastFromTo.findIndex((item: string) => item === setValue)
  if (existingIndex !== -1) {
    lastFromTo.splice(existingIndex, 1)
  }

  // 历史使用显示限制
  if (lastFromTo.length >= 用户配置.baseConfig.lastDirectionStorageNum) {
    lastFromTo.pop()
  }
  lastFromTo.unshift(setValue)
  setDbStorageItem('lastFromTo', lastFromTo)
}


export function 清除历史记录() {
  setDbStorageItem('lastFromTo', [])
}
