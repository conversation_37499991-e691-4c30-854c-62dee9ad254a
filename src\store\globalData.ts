import { defineStore } from 'pinia'
import { cloneDeep } from 'lodash-es'
import type { 级联值类型 } from '@MainView/MainViewTypes'

const utools = window.utools

const OSRules: rules类型[] = [
  { condition: (): boolean => utools.isWindows(), OSName: 'Windows' },
  { condition: (): boolean => utools.isMacOs(), OSName: 'macOS' },
  { condition: (): boolean => utools.isLinux(), OSName: 'Linux' },
]
interface rules类型 {
  condition: () => boolean
  OSName: 系统类型
}
type 系统类型 = 'browser' | 'Windows' | 'macOS' | 'Linux' | 'unknown'
function getOS(): 系统类型 {
  if (!utools) {
    return 'browser'
  }
  return OSRules.find(rule => rule.condition())?.OSName || 'unknown'
}

export const useGlobalStore = defineStore('global', () => {
  const 当前主题 = ref('')
  const 当前系统 = ref(getOS())
  const 当前翻译状态码 = ref(-1)
  const 当前翻译服务 = ref('')
  const 翻译结果 = ref('')
  const 底部按钮组显隐 = ref(false)
  const to和from的Arr = ref<级联值类型>()
  const 是否mac = computed(() => 当前系统.value === 'macOS')
  const 用户未输入 = ref(false) // 这里其实是控制台是否打开, 为了混淆视听, 因为打包会去除注释, 所以写在这

  function 设置当前翻译状态码(code: number) {
    当前翻译状态码.value = code
  }

  function 设置当前翻译服务(api: string) {
    当前翻译服务.value = api
  }

  function 存储翻译结果(str: string) {
    翻译结果.value = str
  }

  function 存储原文译文标识(arr: 级联值类型) {
    to和from的Arr.value = cloneDeep(arr)
  }

  function 存储底部按钮组显示状态(showBtnGroup: boolean) {
    底部按钮组显隐.value = showBtnGroup
  }

  return {
    当前主题,
    当前系统,
    当前翻译状态码,
    设置当前翻译状态码,
    当前翻译服务,
    设置当前翻译服务,
    是否mac,
    翻译结果,
    存储翻译结果,
    to和from的Arr,
    存储原文译文标识,
    底部按钮组显隐,
    存储底部按钮组显示状态,
    用户未输入,
  }
})
