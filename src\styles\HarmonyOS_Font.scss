@use './HarmonyOS_Font_Rules.scss' as m; // 引入unicode-range规则文件
@use 'sass:map';

$provider: 'free'; // 如果值为'free', 将使用B站的字体文件, 否则使用Netlify字体文件
$weight-str: 'Medium'; // 可选值：'Regular'|'Medium', 不填或填错, 将降级为'Regular'
$font-base-url: 'https://efadmin.netlify.app'; // Netlify字体文件路径
$free-font-base-url: 'https://s1.hdslb.com'; // B站字体文件路径
$font-info-map: (
  'Regular': (
    'family': 'HarmonyOS_Regular',
    'weight': 400,
  ),
  'Medium': (
    'family': 'HarmonyOS_Medium',
    'weight': 500,
  ),
);

// 判断$weight-str的值是否在$font-info-map中存在, 不存在则降级为'Regular'
$correct-weight-str: if(map.has-key($font-info-map, $weight-str), $weight-str, 'Regular');

@mixin generate-font-face($file, $unicodeRange) {
  $c-font-info: map.get($font-info-map, $correct-weight-str);
  $c-font-family: map.get($c-font-info, 'family');
  $c-font-weight: map.get($c-font-info, 'weight');

  @font-face {
    font-family: $c-font-family;
    font-style: normal;
    font-weight: $c-font-weight;
    font-display: swap;

    @if $provider == 'free' {
      src: url('#{$free-font-base-url}/bfs/static/jinkela/long/font/#{$c-font-family}.#{$file}.woff2')
        format('woff2');
    } @else {
      src: url('#{$font-base-url}/font/HarmonyOS_Font_#{$correct-weight-str}/#{$c-font-family}.#{$file}.woff2')
        format('woff2');
    }

    unicode-range: #{$unicodeRange};
  }
}

@each $fontFile, $range in m.$font-range-map {
  @include generate-font-face($fontFile, $range);
}
