import { 汉字和汉字符号正则, 符号数字reg } from '@MainView/MainViewData'
import { replace } from 'lodash-es'
import { detect } from 'tinyld'

/**
 * @param 全部文字 用户输入的全部字符串
 * @returns 去除掉符号和数字的字符串
 */
export function 获取有效输入文字(全部文字: string) {
  return replace(全部文字, 符号数字reg, '')
}
/**
 * @param 全部文字 要获取的字符串
 * @param 获取数量 获取几个字
 * @returns 返回传入字符串中, 有效字符串 (去除数字和英文符号) 的前n个字符串
 */
function 获取字符串有效前n个字(全部文字: string, 获取数量: number) {
  return 获取有效输入文字(全部文字).substring(0, 获取数量)
}

export function 用抽样推测语种(用户输入: string) {
  let result = ''
  const 有效输入 = 获取有效输入文字(用户输入)
  if (有效输入.length < 20) {
    const 第一个字是否汉字 = 汉字和汉字符号正则.test(获取字符串有效前n个字(用户输入, 1))
    result = 第一个字是否汉字 ? 'zh' : 'other'
  }
  else {
    const 抽样数量 = 20
    const 比例 = 0.35
    const 抽样文字 = 获取字符串有效前n个字(用户输入, 抽样数量)
    const 一部分字包含汉字数 = 抽样文字.match(new RegExp(汉字和汉字符号正则, 'gu'))?.length ?? 0
    const 汉字占抽样文字的比例 = Number.parseFloat(
      (一部分字包含汉字数 / 抽样数量).toFixed(3),
    )
    const 抽样文字大多汉字 = 汉字占抽样文字的比例 >= 比例
    result = 抽样文字大多汉字 ? 'zh' : 'other'
  }
  return result
}

export function 用库推测语种(用户输入: string) {
  const 有效输入 = 获取有效输入文字(用户输入)
  const result = detect(有效输入)
  return result
}
