<script setup lang="ts">
const model = defineModel<string | number>()

const groupRef = ref<HTMLDivElement>()
const indicatorRef = ref<HTMLDivElement>()
const { width: groupWidth } = useElementSize(groupRef)

// 存储所有选项的元素引用
const itemsMap = reactive(new Map<string | number, HTMLElement>())

function registerItem(value: string | number, element: HTMLElement) {
  itemsMap.set(value, element)
  updateIndicatorPosition()
}

function unregisterItem(value: string | number) {
  itemsMap.delete(value)
}

function updateIndicatorPosition() {
  if (!indicatorRef.value || !model.value) { return }

  const activeElement = itemsMap.get(model.value)
  if (activeElement) {
    indicatorRef.value.style.left = `${activeElement.offsetLeft}px`
    indicatorRef.value.style.width = `${activeElement.offsetWidth}px`
  }
}

const groupContext = {
  modelValue: model,
  registerItem,
  unregisterItem,
}

provide('segmentedGroupRef', groupContext)

onMounted(() => {
  updateIndicatorPosition()
})

watch(
  [
    () => groupWidth.value,
    () => model.value,
  ],
  () => {
    nextTick(() => {
      updateIndicatorPosition()
    })
  },
  { immediate: true },
)

defineExpose({
  updateIndicatorPosition,
})
</script>

<template>
  <div
    ref="groupRef"
    class="relative h-33px w-fit flex select-none gap-2px rounded-3px bg-#F2F3F5 px-4px pb-1px dark:bg-#404143"
    role="tablist"
  >
    <slot />
    <div
      ref="indicatorRef"
      bg="#fff dark:#222"
      class="pointer-events-none absolute-y-center h-[calc(100%-6px)] rounded-2px shadow-sm transition-cusbezier-200 dark:(text-#fff)"
      aria-hidden="true"
    />
  </div>
</template>

<style scoped lang="scss">
</style>
