<template>
  <div
    class="relative aspect-ratio-square w-20px flex-c inline-flex transition-all-400"
    :class="[矫正css, 发光显示条件 && 'shadow_wrapper']"
  >
    <!-- [&:nth-child(2)]是第二个图标发光的效果 -->
    <div
      v-for="item in 循环次数"
      :key="item"
      :style="{ fontSize: `${图标尺寸}px` }"
      class="api_icon_main [&:nth-child(2)]:(absolute bottom-1 right-3px z-[-1] scale-90 op-80 blur-6.5px contrast-200% will-change-filter)"
      :class="图标的值"
    />
  </div>
</template>

<script setup lang="ts">
import type { 首页api类型 } from '@/store/useUserSetting'
import type { 翻译服务类型 } from '@/types'
import { useGlobalStore } from '@/store/globalData'

const { data, current } = defineProps<{
  data: 首页api类型 | 翻译服务类型
  current?: string
}>()
const { 当前主题 } = storeToRefs(useGlobalStore())

const api的值 = computed<翻译服务类型>(() => {
  if (typeof data === 'string') {
    return data
  }
  else {
    return data.value as 翻译服务类型
  }
})

const iconMap = new Map([
  ['ali', { name: 'i-cus-ali', size: 19, extraCss: '' }],
  ['baidu', { name: 'i-cus-baidu', size: 16, extraCss: '-translate-y-1px' }],
  ['caiyun', { name: 'i-cus-caiyun', size: 17, extraCss: '' }],
  ['deepl', { name: 'i-cus-deepl?mask', size: 18, extraCss: 'text-#042B48 dark:text-#fff' }],
  ['google', { name: 'i-cus-google', size: 16, extraCss: '' }],
  ['huoshan', { name: 'i-cus-huoshan', size: 16, extraCss: '-translate-y-1px' }],
  ['tencent', { name: 'i-cus-tencent', size: 17, extraCss: '' }],
  ['transmart', { name: 'i-cus-transmart?mask', size: 17, extraCss: 'text-#003893 dark:text-#fff' }],
  ['xiaoniu', { name: 'i-cus-xiaoniu', size: 16, extraCss: '' }],
  ['youdao', { name: 'i-cus-youdao', size: 16, extraCss: '' }],
  ['yi', { name: 'i-cus-yi?mask', size: 16, extraCss: '' }],
])

const 图标的值 = computed(() => {
  return iconMap.get(api的值.value)?.name || ''
})

const 现在是深色 = computed(() => {
  return 当前主题.value === 'dark'
})

const 选中了自己 = computed(() => {
  return current === api的值.value
})

// 靠vue的循环次数来控制背景发光效果, deepl和transmart的logo因为在深色下是白色的所以不需要发光
// 所以只有在深色主题, 选中了自己, 且不是deepl和transmart的时候才需要发光
const 循环次数 = computed(() => {
  const 深色下不用发光的图标 = ['deepl', 'transmart']
  const 深色且选中 = 现在是深色.value && 选中了自己.value
  const 当前不用发光 = !深色下不用发光的图标.includes(api的值.value)
  return 深色且选中 && 当前不用发光 ? 2 : 1
})

const 发光显示条件 = computed(() => {
  return 现在是深色.value && 选中了自己.value
})

const 图标尺寸 = computed(() => {
  return iconMap.get(api的值.value)?.size || 17
})

// 某些api的logo有一些偏移或者重心不在中心, 需要做一些css矫正
const 矫正css = computed(() => {
  return iconMap.get(api的值.value)?.extraCss || ''
})
</script>

<style lang="scss" scoped>
.shadow_wrapper {
  filter: drop-shadow(0 2px 7px #0e0e0e);
}
</style>
