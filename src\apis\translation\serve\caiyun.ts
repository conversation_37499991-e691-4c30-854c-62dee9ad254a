/**
 * 彩云小译
 * https://docs.caiyunapp.com/blog/2018/09/03/lingocloud-api/
 */

import axios from 'axios'
import { type 翻译参数Type, 返回状态码及信息 } from '../common'

const last = {
  optionsStr: '',
  result: '',
}

/**
 * 通用翻译
 * @param {string} options.q 请求翻译query(UTF-8编码)
 * @param {string} options.from 翻译源语言(可设置为auto)
 * @param {string} options.to 翻译目标语言 (可设置为auto)
 * @param {object} options.keyConfig key配置
 */
export async function textTranslation({ q, from, to, keyConfig }: 翻译参数Type) {
  const url = import.meta.env.VITE_CAIYUN_BASEURL

  // 翻译方式
  const direction = `${from}2${to}`
  const data = {
    source: q,
    trans_type: direction,
    request_id: 'demo',
    detect: true,
  }

  const headers = {
    'content-type': 'application/json',
    'x-authorization': `token ${keyConfig.token}`,
  }

  try {
    const apiRes = await axios.post(url, data, { headers })
    const { target } = apiRes.data

    if (target) {
      // 翻译成功
      last.result = 返回状态码及信息(200, { text: target })
    }
    else {
      // 翻译失败, 可以考虑添加更多错误信息
      last.result = 返回状态码及信息(500, { error: '翻译失败, 没有返回目标文本' })
    }

    return last.result
  }
  catch (error: any) {
    return 返回状态码及信息(500, null, error)
  }
}
