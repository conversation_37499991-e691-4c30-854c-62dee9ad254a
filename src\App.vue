<template>
  <MainView />
</template>

<script setup lang="ts">
import { 获取并保存本地与网络时间差 } from '@/apis/time'
import { useGlobalStore } from '@/store/globalData'
import { userStore } from '@/store/useUser'
import { 用户配置存储 } from '@/store/useUserSetting'
import { setTheme, type 主题风格 } from '@/utils/setTheme'
import MainView from '@MainView/index.vue'
import '@MainView/useExit'

const { 当前主题 } = storeToRefs(useGlobalStore())
const cssStr = ref('')
const darkStyleStr = `body[arco-theme='dark'] {--arcoblue-6: var(--primary-rgb-color)};}`
const { css } = useStyleTag(cssStr)
// 虽然深色浅色的主题色一致, 但是Arco主题色设置过后, 深色模式下会对主题色进行一个偏移, 这里强制指定深色下主题色的颜色值
// 此处因为css内容不多,不用load和unload处理, 而是直接动态改变<style>中的内容
watchEffect(() => {
  css.value = 当前主题.value === 'light' ? '' : darkStyleStr
})

const baseConfig = 用户配置存储().baseConfig
const 主题色 = computed(() => baseConfig.theme)

watchEffect(() => {
  const 系统颜色 = usePreferredColorScheme()
  const theme = 主题色.value === 'auto' ? 系统颜色.value : 主题色.value
  setTheme(theme as 主题风格)
})

function 存储临时密钥() {
  const user = userStore()
  // const date = 根据时间差计算网络时间()
  const k1 = import.meta.env.VITE_G_KEY
  // user.setKey(`${date}@${k1}`)
  user.setKey(k1)
}

onMounted(async () => {
  获取并保存本地与网络时间差()
  存储临时密钥()
})
</script>
