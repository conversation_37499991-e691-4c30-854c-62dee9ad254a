<template>
  <!-- 开关组件 -->
  <div class="flex">
    <input
      id="switch"
      v-model="isChecked"
      class="cus_switch transition-all"
      type="checkbox"
    >
    <label class="cus_switch_label bg-gray dark:bg-#3c3c3f" for="switch"> 切换 </label>
  </div>
</template>

<script setup lang="ts">
const isChecked = defineModel<boolean>('modelValue', { default: false })
</script>

<style lang="scss" scoped>
.cus_switch_label {
  cursor: pointer;
  text-indent: 9999px;
  overflow: hidden;
  width: 40px;
  height: 24px;
  display: block;
  border-radius: 9999px;
  position: relative;
  user-select: none;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 4px;
    height: 70%;
    aspect-ratio: 1/1;
    background-color: #fff;
    border-radius: 9999px;
    transition: all 0.15s var(--ani-bezier);
  }

  // 点击时的样式
  // &:active:after {
  //   aspect-ratio: 4/3;
  // }
}

.cus_switch {
  width: 0;
  height: 0;
  visibility: hidden;
  user-select: none;

  &:checked {
    & + .cus_switch_label {
      background-color: var(--primary-color);

      &::after {
        left: calc(100% - 4px);
        transform: translate(-100%, -50%);
      }
    }
  }
}
</style>
