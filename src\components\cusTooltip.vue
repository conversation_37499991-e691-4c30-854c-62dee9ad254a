<template>
  <!-- 自动改背景颜色的Tooltip组件 -->
  <a-tooltip
    v-bind="$attrs"
    mini
    :background-color="当前主题 === 'light' ? '#333' : '#555'"
  >
    <template #content>
      <slot name="content" />
    </template>
    <slot />
  </a-tooltip>
</template>

<script setup lang="ts">
import { useGlobalStore } from '@/store/globalData'

defineOptions({
  name: 'CusTooltip',
  inheritAttrs: false,
})
const { 当前主题 } = storeToRefs(useGlobalStore())
</script>

<style lang="scss" scoped></style>
