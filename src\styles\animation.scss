// 动画效果

// ==========缩放的效果==========
.component-scale-enter-active, .component-scale-leave-active {
  transition: transform 0.3s var(--ani-bezier);
}

.component-scale-enter-from, .component-scale-leave-to {
  transform: scale(0);
}

// ==========渐显渐隐的效果==========
.custom-fade-enter-active, .custom-fade-leave-active {
  transition: opacity 0.25s var(--ani-bezier);
}

.custom-fade-enter-from, .custom-fade-leave-to {
  opacity: 0;
}

.custom-fade-leave-active {
  position: absolute;
}

// 用于生成组件动画的缩放效果, lb是左下角, rb是右下角, lt是左上角, rt是右上角, img是图片弹窗特有的位置
// 如zoom-br就是从右下角缩放的效果
@each $dir, $value in (lb: 0% 100%, rb: 100% 100%, lt: 0% 0%, rt: 100% 0% ,img: 34% 100%) {
  .zoom-#{$dir}-enter-from,
  .zoom-#{$dir}-appear-from,
  .zoom-#{$dir}-leave-to {
    transform: scale(0, 0);
    opacity: 0.1;
  }

  .zoom-#{$dir}-enter-to,
  .zoom-#{$dir}-appear-to,
  .zoom-#{$dir}-leave-from {
    transform: scale(1, 1);
    transform-origin: $value;
    opacity: 1;
  }

  .zoom-#{$dir}-enter-active,
  .zoom-#{$dir}-appear-active,
  .zoom-#{$dir}-leave-active {
    transform-origin: $value;
    transition: all 0.35s var(--ani-bezier);
  }
}

// 命名模式下的<code/>动画
@keyframes code-bg-animated {
  0% {
    fill: #0000;
    stroke: var(--code-0-stroke);
    stroke-dashoffset: 25%;
    stroke-dasharray: 0 50%;
    stroke-width: 0.8;
  }

  50% {
    fill: #0000;
    stroke: var(--code-50-stroke);
    stroke-width: 1.2;
  }

  70% {
    fill: #0000;
    stroke: var(--code-70-stroke);
    stroke-width: 1.5;
  }

  90%, 100% {
    fill: var(--code-90-color);
    stroke: var(--code-90-color);
    stroke-dashoffset: -25%;
    stroke-dasharray: 50% 0;
    stroke-width: 0;
  }
}
