<template>
  <component
    :is="渲染标签"
    ref="组件的Ref"
    class="size-fit flex"
  >
    <transition name="custom-fade">
      <i
        v-if="hover时候的class && 组件被hover"
        :class="[hover时候的class]"
      />

      <i
        v-else
        :class="[默认图标class]"
      />
    </transition>
  </component>
</template>

<script setup>
defineProps({
  默认图标class: {
    type: String,
    required: true,
  },
  hover时候的class: {
    type: String,
  },
  渲染标签: {
    type: String,
    default: 'span',
  },
})

const 组件的Ref = ref()
const 组件被hover = useElementHover(组件的Ref)
</script>
