# uTools 易翻翻译

## 插件简介

通过接入常见翻译平台的服务, 来支持多种语言互翻。 (开发者：Lainbo & Milo)
面向程序员：我们支持了程序员常用的变量命名翻译功能, 驼峰、下划线、中划线等12种模式, 全程不用碰鼠标即可完成类似「用户姓名」到「UserName」的自动上屏, 优雅地解决取变量名的问题。
面向语言学习者：我们拥有非常棒的朗读发音功能, 除了标准的美式发音, 其他发音一样优秀, 朗读效果第一梯队。
易翻的翻译能力来自于第三方服务, 如果你肯花一点点时间来配置第三方服务, 那么易翻将非常好用。

## 插件特性

1. 接入11种翻译服务：谷歌、有道、百度、DeepL、DeepLX、彩云、小牛、腾讯交互、火山、腾讯云、阿里云
2. 图片翻译
3. 支持翻译结果二次编辑
4. 可设置去除译文换行, pdf翻译友好
5. 支持快捷键复制, 可设置自动输入翻译内容「Ctrl+Shift+C / Command+Shift+C」
6. 可设置三击退格清空, 能不碰鼠标绝对不碰
7. 变量命名翻译模式 (大小驼峰、中下划线、大小分词、对象属性、文件路径、常量、英文标题)
8. 自动深色模式 (将uTools软件或操作系统改为深色均可触发)
9. 更优秀的字体展示, 低分辨率的Windows用户也能获得和Mac用户一样优秀的文字表现, 不用再看那又扁又长还有锯齿的微软雅黑
10. 翻译结果多国语音支持朗读、无需网络的离线朗读
11. 导入导出你起早贪黑申请的配置文件

## 服务申请说明

[易翻插件的相关服务申请说明](https://flowus.cn/share/0d96c879-2dba-4bfc-9d81-4b4f435398e8)

## 翻译支持的语言

|           | 百度翻译 | 腾讯翻译 | 谷歌翻译 | 阿里翻译 | 有道翻译 | 彩云小译 | 火山翻译 | 小牛翻译 | 腾讯交互翻译 | DeepL |
| --------- | -------- | -------- | -------- | -------- | -------- | -------- | -------- | -------- | ------------ | ----- |
| 中文-简体 | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅            | ✅     |
| 英语      | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅            | ✅     |
| 日语      | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅            | ✅     |
| 俄语      | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅        | ✅            | ✅     |
| 韩语      | ✅        | ✅        | ✅        | ✅        | ✅        |          | ✅        | ✅        | ✅            | ✅     |
| 德语      | ✅        | ✅        | ✅        | ✅        | ✅        |          | ✅        | ✅        | ✅            | ✅     |
| 法语      | ✅        | ✅        | ✅        | ✅        | ✅        |          | ✅        | ✅        | ✅            | ✅     |
| 西班牙语  | ✅        | ✅        | ✅        | ✅        | ✅        |          | ✅        | ✅        | ✅            | ✅     |
| 葡萄牙语  | ✅        | ✅        | ✅        | ✅        | ✅        |          | ✅        | ✅        | ✅            | ✅     |
| 泰语      | ✅        | ✅        | ✅        | ✅        | ✅        |          | ✅        | ✅        |              |       |
| 中文-繁体 | ✅        | ✅        | ✅        | ✅        | ✅        |          | ✅        | ✅        |              |       |
| 粤语      | ✅        |          |          | ✅        | ✅        |          |          | ✅        |              |       |
| 文言文    | ✅        |          |          |          |          |          |          |          |              |       |

橙色名称表示该服务不支持任意语言相互翻译, 可能会出现
如：支持中文→日语, 但是不支持法语→日语、俄语→日语, 详细规则以实际功能呈现为准

## 代码模式快捷键

通过以下快捷键可以直接进入命名翻译模式, 帮助你快速命名

1. mmxt (“命名小驼”的首字母) ：小驼峰模式, 如 "getUserInformation"
2. mmdt/mmpsk (“命名大驼/命名帕斯卡”的首字母) ：大驼峰/帕斯卡模式, 如 "GetUserInformation"
3. mmxh (“命名下划”的首字母) ：下划线模式, 如 "get_user_information"
4. mmzhx (“命名中划小”的首字母) ：中划线小写模式, 如 "get-user-information"
5. mmzhd (“命名中划大”的首字母) ：中划线大写模式, 如 "Get-User-Information"
6. mmfcx (“命名分词小”的首字母) ：分词小写模式, 如 "get user information"
7. mmfcd (“命名分词大”的首字母) ：分词大写模式, 如 "Get User Information"
8. mmdx (“命名对象”的首字母) ：对象属性模式, 如 "get.user.information"
9. mmwj (“命名文件”的首字母) ：文件路径模式, 如 "get/user/information"
10. mmcl (“命名常量”的首字母) ：常量模式, 如 "GET_USER_INFORMATION"
11. mmclzx (“命名常量中线”的首字母) ：常量模式, 如 "GET-USER-INFORMATION"
12. mmbt (“命名标题”的首字母) ：英文标题模式, 如 "What Are Some Interesting Translations Of Chinese Food"

## 插件截图

![](doc/1.png)

![](doc/2.png)

![](doc/3.png)

![](doc/4.png)

![](doc/5.png)

![](doc/6.png)

## 贡献者

感谢所有已经为易翻插件做出贡献的人！

[Lainbo - GitHub.com](https://github.com/lainbo)

[Milo - GitHub.com](https://github.com/rkrv01)

## 开发说明

### 安装依赖

```bash
yarn
```

### 开发环境运行

修改插件配置文件中的 preload 路径, 并配置开发环境入口为本地服务

/public/plugin.json

```json
{
  "preload": "./preload/src/index.js",
  "development": {
    "main": "http://127.0.0.1:6789"
  }
}
```

启动开发服务器

```bash
yarn run dev
```

### 打包发布

打包 preload

```bash
yarn run p:build
```

vite 打包

```bash
yarn run build
```

修改打包后插件配置文件中的主入口文件和 preload 路径

/dist/plugin.json

```json
{
  "main": "./index.html",
  "preload": "./preload/dist/index.js"
}
```

### 版本记录

每个版本的详细更改都记录在[版本记录中](./CHANGELOG.md)。

谷歌翻译已恢复。支持多种翻译服务(谷歌|有道|百度|DeepL|DeepLX|彩云|小牛|腾讯交互|火山|腾讯云|阿里云)；支持程序员常用的变量命名 (函数命名) 模式；拥有极具表现力的原文、译文发音朗读；支持导入导出插件设置；ef
