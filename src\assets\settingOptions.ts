export const 快捷键行为选项 = [
  { label: '仅复制', value: 'open' },
  { label: '复制后隐藏插件', value: 'close' },
  { label: '复制隐藏并输入', value: 'closeInput' },
]

export const 翻译触发方式选项 = [
  { label: '输入文字时', value: 'input' },
  { label: '点击翻译按钮/按下回车', value: 'manual' },
]

export const 显示按钮选项 = [
  { label: '仅复制', value: 1 },
  { label: '复制并隐藏', value: 2 },
  { label: '复制并输入', value: 3 },
]

export const 首选译文语言选项 = [
  { label: '英语', value: 'en' },
  { label: '日语', value: 'jp' },
  { label: '俄语', value: 'ru' },
]

export const 插件主题选项 = [
  { label: '跟随uTools', value: 'auto' },
  { label: '浅色', value: 'light' },
  { label: '深色', value: 'dark' },
]

export const 朗读模式选项 = [
  { label: '质量优先', value: '在线' },
  { label: '速度优先', value: '离线' },
]

export const 朗读偏好选项 = [
  { label: '系统默认', value: 'default' },
  { label: '仅男声', value: 'male' },
  { label: '仅女声', value: 'female' },
]

export const DeepL接口选项 = [
  { label: 'Free', value: 'Free' },
  { label: 'Pro', value: 'Pro' },
  { label: 'DeepLX', value: 'deeplx' },
]

export const google接口选项 = [
  { label: '本地', value: 'local' },
  { label: '在线', value: 'online' },
  // { label: '自定义', value: 'custom' },
]
