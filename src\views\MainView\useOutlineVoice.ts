export function 离线朗读主函数() {
  const 要读的文字 = ref('')
  const voice = ref<any>(undefined)
  const {
    isSupported: 支持离线朗读,
    speak: 开始离线朗读,
    status: 离线朗读状态,
    stop,
  } = useSpeechSynthesis(要读的文字, { voice })

  function 初始化离线语音() {
    if (!支持离线朗读.value) {
      return
    }
    let synth: SpeechSynthesis
    setTimeout(() => {
      synth = window.speechSynthesis
      voice.value = synth.getVoices().filter(i => i.lang === 'en-US')[0]
    })
  }

  function 离线朗读开始() {
    离线朗读停止()
    开始离线朗读()
  }

  function 离线朗读停止() {
    stop()
    离线朗读状态.value = 'end'
  }

  function 离线播放函数(str: string) {
    if (!支持离线朗读.value) {
      return
    }
    要读的文字.value = str
    离线朗读状态.value === 'play' ? 离线朗读停止() : 离线朗读开始()
  }

  初始化离线语音()
  return {
    离线播放函数,
    离线朗读状态,
    支持离线朗读,
    离线朗读停止,
  }
}
