/** 用户基本信息 */
import { defineStore } from 'pinia'

// import md5 from 'crypto-js/md5'
import SHA256 from 'crypto-js/sha256'
import { 获取用户信息 } from '@/utils/utools'

// import { 加密 } from '@/utils/rabbit'
export const userStore = defineStore('user', () => {
  const userInfo = reactive({
    avatar: '',
    nickname: '',
    type: '',
    open_id: '',
    member: '',
  })

  const openId = computed(() => userInfo.open_id)

  const gKey = ref('') // appkey 配合谷歌签名

  async function initUserInfo() {
    // 获取完整信息
    const uInfo = await 获取用户信息()
    Object.assign(userInfo, uInfo)
  }

  function setKey(key: string) {
    const pd = import.meta.env.VITE_G_PD
    gKey.value = SHA256(key + pd).toString()
  }

  return {
    userInfo,
    initUserInfo,
    openId,
    setKey,
    gKey,
  }
})
