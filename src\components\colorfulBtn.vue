<template>
  <button
    ref="btnRef"
    ripple="ripple"
    border="~ #eee dark:#444"
    shadow="lg hover:md active:sm dark:#181818"
    text="#333 dark:#ddd"
    bg="#fff dark:#222"
    class="colorful_btn_main relative h-34px w-112px flex-c overflow-hidden rounded-4px transition-all-200 dark:(text-shadow-xl)"
    @mousedown="显示波纹($event)"
  >
    <div class="relative z-20 flex-c space-x-8px">
      <slot name="icon">
        <i :class="iconName" class="text-19px" />
      </slot>
      <div class="colorful_btn_text flex-c leading-none">
        <slot />
      </div>
    </div>
    <div class="ripple_wrapper absolute inset-0">
      <span
        v-for="item in 波纹数组"
        :key="item.key"
        dynamic="true"
        :style="{
          inset: `${item.top}px auto auto ${item.left}px`,
          width: `${item.width}px`,
          height: `${item.height}px`,
        }"
      />
    </div>
  </button>
</template>

<script setup lang="ts">
import { nanoid } from 'nanoid'

defineProps({
  iconName: {
    type: String,
    default: 'i-ep-more-filled',
  },
})
const btnRef = ref() // 按钮的DOM

const 波纹数组 = ref<波纹数组Item[]>([]) // 波纹DOM数组
interface 波纹数组Item {
  key: string
  top: number
  left: number
  width: number
  height: number
}
// 生成波纹DOM
function 显示波纹(e: MouseEvent) {
  const 按钮Dom = btnRef.value
  const 按钮宽度 = 按钮Dom.offsetWidth
  const 按钮位置 = 按钮Dom.getBoundingClientRect()
  const obj: 波纹数组Item = {
    left: e.pageX - 按钮位置.left - 按钮宽度 / 2,
    top: e.pageY - 按钮位置.top - 按钮宽度 / 2,
    width: 按钮宽度,
    height: 按钮宽度,
    key: nanoid(),
  }
  波纹数组.value.push(obj)
}

watch(
  () => 波纹数组.value.length,
  (newVal, oldVal) => {
    if (newVal > oldVal) {
      延迟操作波纹()
    }
  },
)

function 延迟操作波纹(delayTime = 3000) {
  setTimeout(() => {
    删除第一个波纹dom()
  }, delayTime)
}

// 清除第一个波纹
function 删除第一个波纹dom() {
  if (波纹数组.value?.length) {
    波纹数组.value.shift()
  }
}
</script>

<style lang="scss" scoped>
.colorful_btn_main:is(:hover) {
  text-shadow: var(--colorful-btn-text-shadow);
}

.ripple_wrapper {
  span[dynamic='true'] {
    transform: scale(0);
    border-radius: 50%;
    position: absolute;
    background: linear-gradient(
      45deg,
      var(--colorful-btn-color-a),
      var(--colorful-btn-color-b),
      var(--colorful-btn-color-c),
      var(--colorful-btn-color-d)
    );
    animation: ripple 1.5s var(--ani-bezier);
  }
}

@keyframes ripple {
  from {
    opacity: 0.5;
    transform: scale(0);
  }

  to {
    opacity: 0;
    transform: scale(2.5);
  }
}
</style>
