<template>
  <svg viewBox="0 0 50 50" class="spinner h-50px w-50px rounded-full">
    <circle
      class="fill-none transition-all-300"
      stroke="#17181c26 width-[var(--ring-size)] dark:#e7edfe1a"
      cx="25"
      cy="25"
      r="22.5"
    />
    <circle
      stroke="primary width-[var(--ring-size)] dark:#fff"
      class="line transform-origin-[50%_50%] fill-none transition-all-300"
      cx="25"
      cy="25"
      r="22.5"
    />
  </svg>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
.spinner {
  --spinner-size: 4.3;
  --ring-size: 8.5;

  font-size: calc(var(--spinner-size) * 1em);

  .line {
    stroke-linecap: round;
    transform: rotate3d(0, 0, 1, 0deg);
    animation:
      2156ms spinner-arc ease-in-out infinite,
      1829ms spinner-rotate linear infinite;
  }
}

@keyframes spinner-rotate {
  to {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}

@keyframes spinner-arc {
  from {
    stroke-dasharray: 0 150;
    stroke-dashoffset: 0;
  }

  to {
    stroke-dasharray: 100 150;
    stroke-dashoffset: -140;
  }
}
</style>
