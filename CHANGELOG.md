# 版本更新记录

🆕 新增：
💎 优化：
🐛 修复：
🚨 破坏性变更：
💅 样式：
📝 文档：

## 2.21.0
💎 优化：
1. 优化翻译触发方式为手动时（点击按钮/按Enter），Enter不再触发换行，而是使用Shift+Enter换行

## 2.20.0
🆕 新增：
1. 语种翻译选择中，新增最近翻译的 源语言>目标语言 列表

🐛 修复：
1. 修复在一些翻译服务商中，意大利语的语种禁用关系不正确的问题

## 2.19.0
💎 优化：
1. 由于mac系统中分离插件后，在输入法候选过程中按esc会导致输入法和插件一同关闭，所以mac下与官方保持一致，分离插件后不再可以使用esc关闭插件，Windows系统不受本次改动影响

## 2.18.2
💅 样式：
1. 优化部分UI界面样式

## 2.18.1
📝 文档：
1. 移除兔小巢反馈平台的链接（因为他们要收费了，平时在上面处理的问题也确实不多，以后大家在评论区反馈即可）

## 2.18.0
💅 样式：
1. 变更主界面顶部颜色以适应uTools 5的顶栏配色

🚨 破坏性变更：
为了日后语音朗读的稳定性，变更了语音服务的地址，如果你已经将易翻更新到了2.18.0，那就不需要再做什么了。
旧版插件的在线语音朗读功能，将在本版本上架后的第15天失效。

🐛 修复：
1. 修复法语无法正常播放的问题

## 2.17.5
🐛 修复：
1. 修复阿里翻译无法正常使用的问题

## 2.17.4
🐛 修复：
1. 修复未填写翻译服务的key时，没有正确弹出填写提示框的问题
2. 修复无法正常播放音频的问题

💎 优化：
1. DeepLX支持输入等号

## 2.17.3
💎 优化：
1. 翻译请求处理和界面响应性

## 2.17.2
💎 优化：
1. 优化翻译过程Loading动画可能显示不及时的问题
2. 优化网速慢的时候，可能出现的翻译结果与输入文字不匹配的问题
3. 升级代码依赖到更稳定的版本
4. 优化部分动画曲线
5. 优化代码执行效率

## 2.17.1
🐛 修复：
1. 修复腾讯交互翻译的对接，需要额外填一个用户名，详见https://flowus.cn/share/14717519-036e-4d92-9ba6-06d8ee052e7b

## 2.17.0
🚨 破坏性变更：
1. 谷歌翻译后端服务，将在2.17.0上线3天后不再接受之前版本的插件请求，需要使用谷歌翻译的用户请及时更新

🆕 新增：
1. 谷歌翻译响应速度提升，新增优化负载算法
2. 新增额外的本地语种识别算法

💎 优化：
1. 优化快速输入文字时，最终翻译结果还停留在之前的文字问题

## 2.16.3
🐛 修复：
1. 修正 DeepLX 的文档链接

## 2.16.2
📝 文档：
1. 新增 DeepLX 免费接口指引，让每个人都可以用上 DeepL ！（在易翻的设置中，将 DeepL 的接口类型选择为 DeepLX 即可看到）

💎 优化：
1. 设置页面内容未发生改动时，将不再重复触发翻译

## 2.16.1
🐛 修复：
1. 修复可能出现的提示语错误

## 2.16.0
🆕 新增：
1. 谷歌翻译恢复使用

## 2.15.1
🐛 修复：
1. 修复在使用DeepLX时可能出现的“未配置服务”提示

## 2.15.0
🆕 新增：
1. 支持DeepLX, 你可以在 设置页面-DeepL-接口类型 选择「DeepLX」, 并输入相关信息后使用。自己部署的方式参考 https://github.com/OwO-Network/DeepLX

## 2.14.0
🆕 新增：
1. 支持在没有「腾讯交互翻译」的Token时识别语种，从而做到任何情况下都有原文发音功能

💎 优化：
1. 优化推测输入语种的算法

📝 文档：
1. 修复了设置页侧边栏一些已经过时的介绍

## 2.13.0
🆕 新增：
1. 支持自己部署的DeepLX部署

## 2.12.0
🆕 新增：
1. 支持意大利语作为目标语言

💎 优化：
1. 添加腾讯交互翻译新支持的语种

🐛 修复：
1. 修复阿里翻译粤语作为源语言时的语种禁用关系不正确问题
2. 修复彩云小译的语种禁用关系

## 2.11.4
💎 优化：
1. 根据更权威的定义, 优化了命名模式下英文标题的转换规则

## 2.11.3
🐛 修复：
1. 修复代码字体字重不正确的问题

## 2.11.2
🚨 破坏性变更：
1. 由于选项说明可以被收起, 导致很多用户不看说明直接提问, 增加了很多无效的交流, 现已移除设置页面选项说明收起功能

💅 样式：
1. 弹窗弹出动画起点与触发按钮方位保持一致
2. 优化部分SVG资源

💎 优化：
1. 增强代码健壮性
2. 提升英文字体加载速度
3. 优化图片翻译的异常提示文案

## 2.11.1
🐛 修复：
1. 修复windows下图片翻译点击上传图片选择后插件不显示的问题
💎 优化：
1. 优化图片翻译错误提示语


## 2.11.0
🆕 新增：
1. 全新图片翻译上线, 可以通过底栏按钮或关键字进入, 基于百度翻译, 申请文档见
https://flowus.cn/4739fa73-4333-475b-a1e9-d6287eda3b26#0d2cc1c1-c6d3-4e3c-9c40-46cecb97c02a

💎 优化：
1. 优化内部代码逻辑, 提升性能

🐛 修复：
1. 由于uTools的未知原因, 亚克力风格样式会导致在Windows高分辨率显示器上动画比较卡顿, 现已移除亚克力风格代码, 仅保留基础样式, 以保证动画流畅性


## 2.10.5
🐛 修复：
1. 修复白屏问题

## 2.10.3
💅 样式：
1. 移除Mac风格的关闭按钮, 以免在分离窗口时, 视觉难以分辨是哪一个的关闭按钮

## 2.10.2
💅 样式：
1. 微调深色模式下的主要颜色.

🐛 修复：
1. 修复部分界面外观异常

## 2.10.1 把uTools插件介绍中【申请教程】的wolai的url改为新的:'https://flowus.cn/share/0d96c879-2dba-4bfc-9d81-4b4f435398e8'
💎 优化：
1. 优化内部代码逻辑, 提升稳定性

📝 文档：
1. 由于文档服务商分享策略变化, 服务申请的在线文档进行了迁移

💅 样式：
1. 移除切换插件主题时的所有渐变动画, 切换更流畅

## 2.10.0
💅 样式：
1. 新增自动适应mac OS风格的弹窗关闭逻辑

## 2.9.4
🐛 修复：
1. 修复语音朗读的质量模式失效的问题
💎 优化：
1. 优化在按住退格键、回车键不撒手时会高速触发函数的问题
2. 去除一些不必要的网络请求
3. 优化一些文字读取逻辑提升性能
4. 构建工具稳定性更新
5. 更新谷歌安全防护, 对正常uTools用户无影响
💅 样式：
1. 新的亚克力风格弹窗希望你能喜欢


## 2.9.3
🐛 修复：
1. 修正谷歌翻译异常问题
💎 优化：
1. 对代码逻辑进行了改进以提高稳定性。
2. 进行了一些内部调整以加强产品安全性。

## 2.9.2
🐛 修复：
1. 修正了在命名模式下, 输入多个词汇并使用换行分隔, 无法正确输出每个单词的问题

## 2.9.1
🚨 破坏性变更：
1. 小牛翻译的免key使用活动结束, 小牛翻译恢复和其他服务一样的逻辑

💎 优化：
1. 优化了产品性能并修复了一些小 bug。
2. 对代码逻辑进行了改进以提高稳定性。
3. 进行了一些内部调整以加强产品安全性。

## 2.9.0
🚨 破坏性变更：
1. 当使用小牛翻译的公共的API-KEY进行翻译时, 将启用【按键翻译】模式(但是不会改你的设置, 详见“新增”)以减少对公共Key的流量占用。使用自己的Key不会有任何影响。
2. 为什么要这样做呢？因为除了使用公共小牛翻译的用户激增, 导致了服务不稳定外, 我们还发现有人恶意利用和盗用我们提供的小牛翻译Key, 使用脚本进行恶意浪费。小牛翻译实质上并不是免费的, 而是我们在提供Key。这本来是一个帮助新用户下载即用的一个方案, 但这些恶意行为增加了我们的维护成本。因为我们不想对用户施加很多限制和规则, 所以添加了这么一个防君子不防小人的逻辑以观后效。对于正常用户, 你只需要多按一次回车键来触发翻译, 就能帮助我们节省Key的用量, 让我们将精力集中在开发易翻本身上面。感谢您的理解。

🆕 新增：
1. 加入“翻译触发方式”选项, 可自定义在输入文字时【实时翻译】, 还是输入完毕后【按键翻译】

💎 优化：
1. 变更资源加载方式, 提升资源加载速度, 降低产生的维护成本

💅 样式：
1. 优化选项说明的文字颜色
2. 变更设置弹窗头部和底部的纹理玻璃效果
3. 微调了设置页一些小细节

## 2.8.1
🐛 修复：
1. 修复可能出现的关闭某个命名模式后, 下拉框显示不正确的问题
2. 修复插件内汉字显示为旧字形的问题

## 2.8.0
改插件简介, 全局搜索“”, 搜出来的地方都重新粘贴一下

🚨 破坏性变更：
1. 更新到2.8.0后, 需要首先进入一次易翻, 才可以激活命名模式的关键字, 后续正常使用即可

🆕 新增：
1. DeepL翻译服务接入
2. 命名模式设置, 你可以通过右键命名模式图标/设置页面中的按钮, 进入命名模式设置页

💎 优化：
1. 代码稳定性优化
2. 优化翻译加载图标, 不再那么调皮, 弱化“已经转了一圈”这个概念, 缓解焦急的心理情绪
3. 找翻译官方要了翻译服务图标, 原本使用错误的改正确, 本就正确的优化体积并减少绘图次数
4. 优化首屏DOM数量, 提升性能
5. 重新分配代码产物, 平均产物体积
6. 内部代码性能提升, 表现不变的前提下, 减少计算次数或缓存结果
7. 根据Chromium特性优化资源加载, 减少资源请求数量, 优化资源体积
8. 更新代码字体、韩文字体、泰文字体
9.  针对性优化结果中的日文字体, 避免造成使用中文渲染日文, 导致的中日文混排 (如果填写了腾讯交互的Token, 输入的日文也可被优化)
10. 所有字体更细粒度的字体文件加载控制, 把以前的按语言控制加载, 改为了按文字unicode范围加载。更快的显示目标字体
11. 优化初始化加载任意关键字的逻辑

💅 样式：
1. 插件内Logo支持动态颜色响应
2. Logo像素点优化
3. 优化可能出现的滚动条样式不正确的问题
4. 优化拟态按钮的内部缩放策略
5. 优化深色模式下选中文字的可辨识度
6. 优化底栏收缩机制
7. 引导层支持深色模式
8. 部分服务图标变更
9. 优化了检测语言的动画效果

🐛 修复：
1. 设置页面部分文案不正确的问题
2. 修复海外用户因为时区问题而无法使用部分翻译服务的问题

## 2.7.2
🐛 修复：
1. 修正泰文字体不正常的问题

## 2.7.1
💎 优化：
1. 命名模式下添加了一些动态提示性文案
2. 原文发音角色将跟随用户选择的语言进行朗读
3. 不填小牛翻译key的情况下, 小牛翻译字数限制增加到350字

🐛 修复：
1. 处理本地处理命名模式时, 无法通过按钮和快捷键复制的问题

## 2.7.0
🆕 新增：
1. 命名模式逻辑修改, 尽可能减少可能的翻译服务的使用量

💅 样式：
1. 修复字体失效的问题

💎 优化：
1. 变更部分已经过时的文案

🐛 修复：
1. 修复复制文本无法带入文本框的问题


## 2.6.2
💎 优化：
1. 变更中文默认和女性朗读声音
2. 增加系统稳定性

🐛 修复：
1. 修复谷歌翻译、有道翻译在翻译文字有特殊符号时的报错问题

## 2.6.1
💅 样式：
1. 优化部分动画

💎 优化：
1. 增加系统稳定性

🐛 修复：
1. 修复部分关键字错误问题
2. 修复朗读在utools下有时候失败的问题
3. 修复底栏在Loading时会被遮挡的bug


## 2.6.0
🆕 新增：
1. 支持在线模式和离线模式的原文朗读 (离线模式仅支持英文)
2. 新增命名翻译模式：常量(中线)
3. 底栏新增重置下方文本框高度的按钮

💎 优化：
1. 谷歌翻译调用服务优化

🐛 修复：
1. 质量朗读部分情况下的异常处理


## 2.5.3
💅 样式：
1. 优化所有滚动条可用性
2. 优化顶部提示信息与背景的视觉层次关系
3. 优化设置页「主页显示顺序」的视觉效果
4. 统一了一些犄角旮旯地方的配色

🐛 修复：
1. 修复设置页滚动条几乎不能点击的问题
2. 变更了一些已经描述有偏差的文案

## 2.5.2
🐛 修复：
1. 修复关闭自动判断语种时, 输入框没有自动聚焦的问题

## 2.5.1
🆕 新增：
1. 底栏新增服务申请快捷按钮

🐛 修复：
1. 修复了一个预期之外的撤销按钮显示时机, 保证清除按钮的正常显示
2. 修复了某些翻译服务下, 加载文字不匹配的问题

## 2.5.0
🆕 新增：
1. 腾讯交互翻译
2. 底部工具类新增“译文编辑”功能按钮
3. 命名翻译模式新增“英文标题”转换规则
4. 新增3击退格清空输入框功能
5. 新增清空输入后的撤销清空功能

💎 优化：
1. 优化不同分辨率下的显示逻辑, 使可选翻译服务数量上限由4个增加至7个
2. 优化深色模式下, 高亮图标的质感
3. 优化工具栏交互, 切换“去除换行”开关后, 将会自动触发重新翻译
4. 优化智能语种判断交互, 重新打开智能判断语种开关后, 将自动触发一次翻译
5. 变更设置页中的翻译服务相关配置的顺序
6. 修复复制按钮可能因类似健壮性测试的点击后, 导致的异常内存增长
7. 修复个别用户出现的底部工具栏在第一次进入会卡在一半的问题
8. 优化底栏触发区域
9. 优化翻译逻辑, 避免连续快速翻译带来的问题

🐛 修复：
1. 修复语音朗读质量优先模式朗读失败的问题

## 2.4.2
💎 优化：
1. 优化底栏点击性能
2. 部分UI细节调整

🐛 修复：
1. 修复底栏切换“去除译文换行”功能开关时, 提示文字不正确的问题
2. 修复底栏切换朗读模式时, 之前未结束的朗读不会自动停止的问题
3. 修复开始翻译时, 离线朗读不会自动停止的问题
4. 修复朗读功能的相关按钮可能与翻译结果文字发生重叠的问题

## 2.4.0


🆕 新增：
1. 增加快捷底栏 (鼠标在插件主页底部悬停可唤出)

💎 优化：
1. 优化部分界面

🐛 修复：
2. 修复翻译换行符问题

## 2.3.1
💎 优化：
1. 优化部分界面交互及文案

🐛 修复：
1. 修复谷歌翻译报错问题(自2022年9月28日起, 谷歌翻译退出了中国市场, 本插件的谷歌翻译接口也受到了影响并及时调整, 目前只能够对一部分用户提供谷歌翻译的使用, 无法使用的用户可以选择其他的几个翻译api)
2. 修复翻译末尾换行符问题

## 2.3.0


🆕 新增：
1. 添加翻译服务「小牛翻译」
2. 增加对葡萄牙语的支持
2. 增加去除换行符设置项

💎 优化：
1. 优化部分界面和图标的交互
2. 优化项目稳定性
3. 插件简介图片变更

🐛 修复：
1. 修复命名模式图标颜色不正确的问题
2. 修复一些快捷键问题
3. 修复谷歌翻译报错问题

## 2.2.2


🆕 新增：
1. 右键文字粘贴翻译内容

💎 优化：
1. 修改部分文案
2. 优化部分UI交互
3. 优化资源加载策略
4. 针对mac优化快捷键触发策略

## 2.2.1


🆕 新增：
1. 朗读模式配置：质量优先、速度优先
2. 语言朗读快捷键 「Ctrl+Shift+S」「Command+Shift+S」

💎 优化：
1. 更换部分字体
2. 优化api图标样式
3. 优化部分UI交互
3. 增强插件的稳定性

🐛 修复：
1. 修复语言朗读无响应的问题

## 2.1.1


💎 优化：文案修改

## 2.1.0


🆕 新增：
1. api服务语言预测外显

💎 优化：
1. 优化滚动条样式
2. 优化设置弹窗样式
3. 增强插件的稳定性
4. 优化部分暗色UI

🐛 修复：
1. 修复彩云小译语种选项禁用关系不正确的问题

## 2.0.0


🆕 新增：
1. UI界面升级

💎 优化：
1. 插件架构重构升级, 增强插件的稳定性及扩展性
2. 优化文案

## 1.7.2

优化：
1. 翻译服务顺序优化
2. 增强插件稳定性

## 1.7.1

优化：
1. 优化智能语种识别算法
2. 快捷键修正
3. 优化代码单词的翻译效果
4. 简介文案更新

## 1.7.0

新增：
1. 增加泰语、西班牙语的翻译及语音朗读
2. 新增设置导入、导出, 非utools会员也可以享用数据同步了
3. 新增翻译结果可编辑, 使用「Ctrl/Command+鼠标右键」点击结果文本框, 即可开启/关闭编辑
优化：优化UI显示

## 1.6.0

优化：
1. 优化项目开发构建过程, 提高项目稳定性
2. 部分UI样式调整, 包括部分图标以及主题风格
3. 语音朗读优化

## 1.5.3

优化：部分关键字合并归类；
修复：任意文本关键字带入输入框的问题

## 1.5.2
新增：
优化：
1. 分离窗口时使用ESC退出插件
2. 窗口回显时自动聚焦首页输入框
3. 页面文案提示更新
修复：修复已知bug

## 1.5.1

新增：
1. AI识别语言语言并翻译至目标外语(目前支持英、日、俄)
2. 关键字调整, 突出任意文本关键字的职能, 新增【fjyi】关键字
优化：
1. 优化设置弹窗动画
2. 移除命名翻译模式的设置存储, 可通过命名模式专属关键字【mmxx】来打开命名翻译模式, 或在首页切换命名翻译模式, 状态将保持直到插件退出
3. UI优化
修复：修复已知bug

## 1.4.1

修复：修复设置页申请链接点击失效的问题

## 1.4.0

新增：
1. 新增粤语翻译, 并适配对应语音朗读
2. 新增主题设置选项
优化：
1. 改变翻译语言下拉形式为联级选择以提高语言选择的准确性
2. 优化动画效果,去除一些不必要的动画
3. 微调UI布局
4. 优化韩文字形
5. 提高插件运行稳定性

## 1.3.0

新增：语音朗读功能

## 1.2.4

修复：火山翻译报错问题

## 1.2.3

新增：命名翻译模式出场动效；
优化：调整设置窗口动画效果；调整UI样式；

## 1.2.2

优化：调整插件打开后命名翻译模式启动逻辑, 仅使用 mm[xx] 关键字启动时才使用命名翻译模式

## 1.2.1

优化：交互提示优化
修复：修正了复制并输入失效的问题

## 1.2.0

新增：首页按钮显示配置, 方便应对不同的情况时快速操作
优化：UI细节优化, 性能优化

## 1.1.0

新增：火山翻译；
优化：UI 交互效果；
修复：腾讯翻译由于时区带来的签名错误问题

## 1.0.4

修复：因 tab 键屏蔽导致的设置页面 tab 失效的异常问题

## 1.0.3

1. 新增快捷键切换翻译方式的功能, 在首页点击 tab 键就可以切换翻译方式啦
2. 其他内容更新

## 1.0.2

1. 新增其他插件通过翻译关键字跳转功能 (如：OCR-图片转文字识别图片后点击去翻译)
2. 部分业务逻辑优化
3. 文案调整
4. 优化包体积, 更快速的下载

## 1.0.1

1. 优化插件体积, 更省空间
2. 更名为「易翻翻译」
3. 更新描述信息

## 1.0.0

1. 多种语言互翻
2. 自定义翻译方式
3. 自定义输入框文字大小
4. 支持一键复制、快捷键复制 (Ctrl+Shift+C / Command+Shift+C)
5. 命名翻译模式 (大小驼峰、中下划线、大小分词、对象属性、文件路径、常量)
6. 自动深色模式 (将utools软件或操作系统改为深色均可触发)
