{"name": "utools-easy-translation", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "vite build && cd dist/preload && ni", "c-b": "pnpm run clear && pnpm run build", "clear": "rimraf node_modules && pnpm i", "clearForWin": "rm -r -fo node_modules && pnpm i", "lint:fix": "eslint . --fix", "stylelint:fix": "stylelint **/*.{vue,css,scss} --fix", "up": "ncu -i --format group"}, "dependencies": {"@fontsource/inter": "^5.2.6", "@fontsource/jetbrains-mono": "^5.2.6", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "change-case": "^5.4.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "nanoid": "^5.1.5", "pinia": "^3.0.3", "qs": "^6.14.0", "shepherd.js": "11.2.0", "tinyld": "^1.3.4", "title-case": "^4.3.2", "validator": "^13.15.15", "vue": "^3.5.16"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@arco-design/web-vue": "^2.57.0", "@iconify/json": "^2.2.347", "@stylistic/stylelint-plugin": "^3.1.2", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.8", "@types/qs": "^6.14.0", "@types/validator": "^13.15.1", "@unocss/eslint-plugin": "^66.2.0", "@unocss/reset": "^66.2.0", "@vitejs/plugin-vue": "^5.2.4", "eslint": "^9.28.0", "less": "^4.3.0", "sass": "^1.89.2", "stylelint": "^16.20.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "terser": "^5.42.0", "typescript": "^5.8.3", "unocss": "^66.2.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-plugin-javascript-obfuscator": "^3.1.0", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^2.2.10"}}